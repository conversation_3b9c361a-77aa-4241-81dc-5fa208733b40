<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file original="admin-dev/themes/default/template/controllers/access/helpers/form/form.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="b61541208db7fa7dba42c85224405911">
        <source>Menu</source>
        <target>Menu</target>
        <note>Line: 274</note>
      </trans-unit>
      <trans-unit id="1587f391edb6fe6f3b919aaa27e25cfb">
        <source>No menu</source>
        <target>No menu</target>
        <note>Line: 284</note>
      </trans-unit>
    </body>
  </file>
  <file original="admin-dev/themes/default/template/controllers/shop/helpers/form/form.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="99104e1966b34f0320d33c5892946a82">
        <source>Import data from another shop</source>
        <target>Import data from another shop</target>
        <note>Line: 75</note>
      </trans-unit>
    </body>
  </file>
  <file original="classes/lang/KeysReference/FeatureFlagLang.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="25068573b3baa0119df0d2922ffe0a86">
        <source>Experimental product page</source>
        <target>Experimental product page</target>
        <note>Line: 28
Comment: Product feature flag in 1.7.8</note>
      </trans-unit>
      <trans-unit id="a04bb916ef42b1e993d1f3752136fabf">
        <source>New product page - Single store</source>
        <target>New product page - Single store</target>
        <note>Line: 35
Comment: Product feature flag in 8.0</note>
      </trans-unit>
      <trans-unit id="084295e69ff6473c0a61b9be44f852d2">
        <source>New product page - Multistore</source>
        <target>New product page - Multistore</target>
        <note>Line: 41
Comment: Product multi store feature flag in 8.0</note>
      </trans-unit>
      <trans-unit id="7871566bb5f25ceb0ebdfa6ad1df25ba">
        <source>New product page</source>
        <target>New product page</target>
        <note>Line: 37
Comment: Product feature flag in 8.1</note>
      </trans-unit>
      <trans-unit id="221c8f123fabfbf450f0dd6923812bba">
        <source>Authorization server</source>
        <target>Authorization server</target>
        <note>Line: 49
Comment: Authorization server feature flag</note>
      </trans-unit>
      <trans-unit id="e5bfab8b7f97b8f0b19ad7e393d7c566">
        <source>Catalog price rules</source>
        <target>Catalog price rules</target>
        <note>Line: 57
Comment: Catalog price rules feature flag</note>
      </trans-unit>
      <trans-unit id="29c21e348106b6651c7d6e96b19de567">
        <source>Enable / Disable having more than one image format (jpg, webp, avif, png, etc.)</source>
        <target>Enable / Disable having more than one image format (jpg, webp, avif, png, etc.)</target>
        <note>Line: 61
Comment: Multiple image formats feature flags</note>
      </trans-unit>
      <trans-unit id="790d59ef178acbc75d233bf4211763c6">
        <source>Countries</source>
        <target>Countries</target>
        <note>Line: 64
Comment: Countries feature flag</note>
      </trans-unit>
      <trans-unit id="b8464cdd84b5f068ad72bf5c4f32163d">
        <source>States</source>
        <target>States</target>
        <note>Line: 68
Comment: States feature flag</note>
      </trans-unit>
      <trans-unit id="1d6af794b2599c1407a83029a09d1ecf">
        <source>Carriers</source>
        <target>Carriers</target>
        <note>Line: 72
Comment: Carriers feature flag</note>
      </trans-unit>
      <trans-unit id="8612579044efe457f11398b5a2377226">
        <source>Titles</source>
        <target>Titles</target>
        <note>Line: 76
Comment: Titles feature flag</note>
      </trans-unit>
      <trans-unit id="3d541d3352f36c85628c8dedea668348">
        <source>Tax rule groups</source>
        <target>Tax rule groups</target>
        <note>Line: 84
Comment: Tax rule groups feature flag</note>
      </trans-unit>
      <trans-unit id="207060096360e9c49b413073fe15daaa">
        <source>Customer threads</source>
        <target>Customer threads</target>
        <note>Line: 88
Comment: Customer threads feature flag</note>
      </trans-unit>
      <trans-unit id="0133c7a0fa55069df27f8d9be2c6ab89">
        <source>Order states</source>
        <target>Order states</target>
        <note>Line: 92
Comment: Order states feature flag</note>
      </trans-unit>
      <trans-unit id="d08ccf52b4cdd08e41cfb99ec42e0b29">
        <source>Permissions</source>
        <target>Permissions</target>
        <note>Line: 80
Comment: Permissions feature flag</note>
      </trans-unit>
    </body>
  </file>
  <file original="classes/lang/KeysReference/ProfileLang.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="0b28a5799a32c687dad2c5183718ceac">
        <source>SuperAdmin</source>
        <target>SuperAdmin</target>
        <note>Line: 26</note>
      </trans-unit>
      <trans-unit id="f5130b14aee3fd950fae10a832e4aefe">
        <source>Logistician</source>
        <target>Logistician</target>
        <note>Line: 27</note>
      </trans-unit>
      <trans-unit id="8d4f90eeea46d604a6410c9bd877f582">
        <source>Translator</source>
        <target>Translator</target>
        <note>Line: 28</note>
      </trans-unit>
      <trans-unit id="5ae0061f57027b2344a537eab86c1de2">
        <source>Salesman</source>
        <target>Salesman</target>
        <note>Line: 29</note>
      </trans-unit>
    </body>
  </file>
  <file original="classes/webservice/WebserviceKey.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a9c123edc2f28b4ea69662510b0f2905">
        <source>Webservice key created: %s</source>
        <target>Webservice key created: %s</target>
        <note>Line: 61</note>
      </trans-unit>
      <trans-unit id="369a57a2773b1a4f3436bd6dc986835a">
        <source>Webservice key %s has been deleted</source>
        <target>Webservice key %s has been deleted</target>
        <note>Line: 94</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminImportController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="0440efe0a9a9bc643eaec7dbdade39df">
        <source>Supply Orders</source>
        <target>Supply Orders</target>
        <note>Line: 4646</note>
      </trans-unit>
      <trans-unit id="9f954c5e16659673382f03fa027772ea">
        <source>Supply Order Details</source>
        <target>Supply Order Details</target>
        <note>Line: 4649</note>
      </trans-unit>
      <trans-unit id="fa4ecc0168c2a9e448f469e3835d2a68">
        <source>Product Reference</source>
        <target>Product Reference</target>
        <note>Line: 136</note>
      </trans-unit>
      <trans-unit id="3b0649c72650c313a357338dcdfb64ec">
        <source>Note</source>
        <target>Note</target>
        <note>Line: 538</note>
      </trans-unit>
      <trans-unit id="9b1f5ada3db7e58544806a11099b7103">
        <source>Supplier ID *</source>
        <target>Supplier ID *</target>
        <note>Line: 568</note>
      </trans-unit>
      <trans-unit id="84560d7d7c4d97c640d09a58491a3677">
        <source>Lang ID</source>
        <target>Lang ID</target>
        <note>Line: 569</note>
      </trans-unit>
      <trans-unit id="8954e2e0d62b56db3c10df45a7602cd3">
        <source>Warehouse ID *</source>
        <target>Warehouse ID *</target>
        <note>Line: 570</note>
      </trans-unit>
      <trans-unit id="dfa8db9b8d08689d8a23067e93ef4ef9">
        <source>Currency ID *</source>
        <target>Currency ID *</target>
        <note>Line: 571</note>
      </trans-unit>
      <trans-unit id="e11d1e5d17a3f9ce34faccbb5cc61394">
        <source>Supply Order Reference *</source>
        <target>Supply Order Reference *</target>
        <note>Line: 597</note>
      </trans-unit>
      <trans-unit id="f9c5030e9d71a6ba285489576e1d0e3f">
        <source>Delivery Date (Y-M-D)*</source>
        <target>Delivery Date (Y-M-D)*</target>
        <note>Line: 573</note>
      </trans-unit>
      <trans-unit id="9e57c2dbcacea5e2e8aa3edde11cf976">
        <source>Discount rate</source>
        <target>Discount rate</target>
        <note>Line: 574</note>
      </trans-unit>
      <trans-unit id="c5d4da7adbd98e284ef9a8733a9ceaf2">
        <source>Product ID *</source>
        <target>Product ID *</target>
        <note>Line: 598</note>
      </trans-unit>
      <trans-unit id="41b00531a619d5e0f965854e352529f1">
        <source>Product Attribute ID</source>
        <target>Product Attribute ID</target>
        <note>Line: 599</note>
      </trans-unit>
      <trans-unit id="94fb72f3353fb2507e18fc6748f7a811">
        <source>Unit Price (tax excl.)*</source>
        <target>Unit Price (tax excl.)*</target>
        <note>Line: 600</note>
      </trans-unit>
      <trans-unit id="f41a10a672cbcdecaad1d2f73f30b1f8">
        <source>Quantity Expected *</source>
        <target>Quantity Expected *</target>
        <note>Line: 601</note>
      </trans-unit>
      <trans-unit id="6703aa9936582b4381418f7d523370b4">
        <source>Discount Rate</source>
        <target>Discount Rate</target>
        <note>Line: 602</note>
      </trans-unit>
      <trans-unit id="8fe77c2601e54f1aaef28cfde997bbad">
        <source>Tax Rate</source>
        <target>Tax Rate</target>
        <note>Line: 603</note>
      </trans-unit>
      <trans-unit id="2415390fb5052bb8221a93c9fc51b17f">
        <source>Import .CSV data</source>
        <target>Import .CSV data</target>
        <note>Line: 889</note>
      </trans-unit>
      <trans-unit id="70b8833184afe1b309f4d679acff991e">
        <source>Advanced Stock Management</source>
        <target>Advanced Stock Management</target>
        <note>Line: 330</note>
      </trans-unit>
      <trans-unit id="278c491bdd8a53618c149c4ac790da34">
        <source>Template</source>
        <target>Template</target>
        <note>Line: 575</note>
      </trans-unit>
      <trans-unit id="d37c2bf1bd3143847fca087b354f920e">
        <source>Customer ID</source>
        <target>Customer ID</target>
        <note>Line: 430</note>
      </trans-unit>
      <trans-unit id="a10d0bff85112a2b35f885a38088cd20">
        <source>Active  (0/1)</source>
        <target>Active  (0/1)</target>
        <note>Line: 428</note>
      </trans-unit>
      <trans-unit id="03361eda68f746619c2ae3341eaa2f07">
        <source>Customer email</source>
        <target>Customer email</target>
        <note>Line: 429</note>
      </trans-unit>
      <trans-unit id="6c7c9fbb27699c4018dc15744547b4a4">
        <source>Root category (0/1)</source>
        <target>Root category (0/1)</target>
        <note>Line: 211</note>
      </trans-unit>
      <trans-unit id="97f08a40f22a625d0cbfe03db3349108">
        <source>Product ID</source>
        <target>Product ID</target>
        <note>Line: 135</note>
      </trans-unit>
      <trans-unit id="8ae880593c2afcc7da6d3530513a05d6">
        <source>Attribute (Name:Type:Position)</source>
        <target>Attribute (Name:Type:Position)</target>
        <note>Line: 138</note>
      </trans-unit>
      <trans-unit id="e1b8b0be45098d71aba659e1149ea395">
        <source>Value (Value:Position)</source>
        <target>Value (Value:Position)</target>
        <note>Line: 141</note>
      </trans-unit>
      <trans-unit id="8284ae5df53e6e7ffc1f2cc67ae68765">
        <source>Supplier reference</source>
        <target>Supplier reference</target>
        <note>Line: 143</note>
      </trans-unit>
      <trans-unit id="5db5f3f0c5f233fbc13f08c052e98281">
        <source>Default (0 = No, 1 = Yes)</source>
        <target>Default (0 = No, 1 = Yes)</target>
        <note>Line: 156</note>
      </trans-unit>
      <trans-unit id="e933b93c26b14c12ac7818867e54c5d9">
        <source>Combination availability date</source>
        <target>Combination availability date</target>
        <note>Line: 157</note>
      </trans-unit>
      <trans-unit id="657645fae83ad1673b25d3c027b77ed8">
        <source>Choose among product images by position (1,2,3...)</source>
        <target>Choose among product images by position (1,2,3...)</target>
        <note>Line: 159</note>
      </trans-unit>
      <trans-unit id="fbd99ad01b92dbafc686772a39e3d065">
        <source>UPC</source>
        <target>UPC</target>
        <note>Line: 260</note>
      </trans-unit>
      <trans-unit id="ac0b7d0703609cbbfa09b53f8d793d29">
        <source>Minimal quantity</source>
        <target>Minimal quantity</target>
        <note>Line: 282</note>
      </trans-unit>
      <trans-unit id="4d2589e1bcd4263cb99927b59f0f88d2">
        <source>Image URLs (x,y,z...)</source>
        <target>Image URLs (x,y,z...)</target>
        <note>Line: 302</note>
      </trans-unit>
      <trans-unit id="7a2ec0911e94664f3dfcc8cbefb0e908">
        <source>Image alt texts (x,y,z...)</source>
        <target>Image alt texts (x,y,z...)</target>
        <note>Line: 303</note>
      </trans-unit>
      <trans-unit id="53acc0172842523c8a52f50894c6df15">
        <source>Depends on stock</source>
        <target>Depends on stock</target>
        <note>Line: 334</note>
      </trans-unit>
      <trans-unit id="6416e8cb5fc0a208d94fa7f5a300dbc4">
        <source>Warehouse</source>
        <target>Warehouse</target>
        <note>Line: 338</note>
      </trans-unit>
      <trans-unit id="a7b4930bf2fc5d57eec6b03097200152">
        <source>EAN-13</source>
        <target>EAN-13</target>
        <note>Line: 259</note>
      </trans-unit>
      <trans-unit id="f323be7152201bfc856e649e84e9d64c">
        <source>Titles ID (Mr = 1, Ms = 2, else 0)</source>
        <target>Titles ID (Mr = 1, Ms = 2, else 0)</target>
        <note>Line: 386</note>
      </trans-unit>
      <trans-unit id="9588fe347c3d1c8b41a762b2de764bf6">
        <source>Birth date (yyyy-mm-dd)</source>
        <target>Birth date (yyyy-mm-dd)</target>
        <note>Line: 389</note>
      </trans-unit>
      <trans-unit id="b884d81a33fb67e1d4fe450b3cbde8d6">
        <source>Newsletter (0/1)</source>
        <target>Newsletter (0/1)</target>
        <note>Line: 392</note>
      </trans-unit>
      <trans-unit id="7a39b81015194bb83112801572292040">
        <source>Partner offers (0/1)</source>
        <target>Partner offers (0/1)</target>
        <note>Line: 393</note>
      </trans-unit>
      <trans-unit id="d6ac7b9d5e3b186f26efc9a5309d8985">
        <source>Registration date (yyyy-mm-dd)</source>
        <target>Registration date (yyyy-mm-dd)</target>
        <note>Line: 394</note>
      </trans-unit>
      <trans-unit id="8ed4a31f87f37636186bd6bdbc646f1f">
        <source>Groups (x,y,z...)</source>
        <target>Groups (x,y,z...)</target>
        <note>Line: 395</note>
      </trans-unit>
      <trans-unit id="b0869dd58b90ec940e91a195d754a155">
        <source>Default group ID</source>
        <target>Default group ID</target>
        <note>Line: 396</note>
      </trans-unit>
      <trans-unit id="df8ffb90c945e796f2cfd9265325b9c3">
        <source>Categories (x,y,z...)</source>
        <target>Categories (x,y,z...)</target>
        <note>Line: 245</note>
      </trans-unit>
      <trans-unit id="8a1cabdfd802aa4ea2ec774feb832a1e">
        <source>Tax rule ID</source>
        <target>Tax rule ID</target>
        <note>Line: 248</note>
      </trans-unit>
      <trans-unit id="b548dcad953710689b3066823b90f517">
        <source>On sale (0/1)</source>
        <target>On sale (0/1)</target>
        <note>Line: 250</note>
      </trans-unit>
      <trans-unit id="b30690be173bcd4a83df0cd68f89a385">
        <source>Discount amount</source>
        <target>Discount amount</target>
        <note>Line: 251</note>
      </trans-unit>
      <trans-unit id="5d01d5588119abec82fd8004995de275">
        <source>Discount percent</source>
        <target>Discount percent</target>
        <note>Line: 252</note>
      </trans-unit>
      <trans-unit id="bfd574f8096a396d831b1c1ac88c75d1">
        <source>Discount from (yyyy-mm-dd)</source>
        <target>Discount from (yyyy-mm-dd)</target>
        <note>Line: 253</note>
      </trans-unit>
      <trans-unit id="48d6c0804dd92fb5463bba154a8a747f">
        <source>Discount to (yyyy-mm-dd)</source>
        <target>Discount to (yyyy-mm-dd)</target>
        <note>Line: 254</note>
      </trans-unit>
      <trans-unit id="6928b30c9f87670d7621fe8f1fef7054">
        <source>Reference #</source>
        <target>Reference #</target>
        <note>Line: 255</note>
      </trans-unit>
      <trans-unit id="c7d4affd54ce760d4731e21e3aa506fd">
        <source>Supplier reference #</source>
        <target>Supplier reference #</target>
        <note>Line: 256</note>
      </trans-unit>
      <trans-unit id="f6db0ad0a1b6aff800f260a83bc06584">
        <source>Delivery time of out-of-stock products with allowed orders:</source>
        <target>Delivery time of out-of-stock products with allowed orders:</target>
        <note>Line: 276</note>
      </trans-unit>
      <trans-unit id="7bbdd372c9d171174e3ce1ba9d97d5f6">
        <source>Additional shipping cost</source>
        <target>Additional shipping cost</target>
        <note>Line: 286</note>
      </trans-unit>
      <trans-unit id="0246175f81454bc445f908bac64f354c">
        <source>Unit for the price per unit</source>
        <target>Unit for the price per unit</target>
        <note>Line: 287</note>
      </trans-unit>
      <trans-unit id="bede52f418b34b1ab0db65f9450bdfc3">
        <source>Tags (x,y,z...)</source>
        <target>Tags (x,y,z...)</target>
        <note>Line: 291</note>
      </trans-unit>
      <trans-unit id="db72e24b402e8baf8df6ca786a59e4de">
        <source>Rewritten URL</source>
        <target>Rewritten URL</target>
        <note>Line: 295</note>
      </trans-unit>
      <trans-unit id="6be65deefdf4fae21955e2c060815530">
        <source>Label when backorder allowed</source>
        <target>Label when backorder allowed</target>
        <note>Line: 297</note>
      </trans-unit>
      <trans-unit id="cf7a1f189bbd1f48db0ece1da9cc0802">
        <source>Available for order (0 = No, 1 = Yes)</source>
        <target>Available for order (0 = No, 1 = Yes)</target>
        <note>Line: 298</note>
      </trans-unit>
      <trans-unit id="d24a6864b6139a2423c8f3004c8f7751">
        <source>Product availability date</source>
        <target>Product availability date</target>
        <note>Line: 299</note>
      </trans-unit>
      <trans-unit id="21f7b5b011f253b35340528b7f190282">
        <source>Product creation date</source>
        <target>Product creation date</target>
        <note>Line: 300</note>
      </trans-unit>
      <trans-unit id="76aeea9f48196068e6b47d46d41e7618">
        <source>Show price (0 = No, 1 = Yes)</source>
        <target>Show price (0 = No, 1 = Yes)</target>
        <note>Line: 301</note>
      </trans-unit>
      <trans-unit id="4f5dc004cdd01ec9cf7fbbcf9f812aa7">
        <source>Delete existing images (0 = No, 1 = Yes)</source>
        <target>Delete existing images (0 = No, 1 = Yes)</target>
        <note>Line: 305</note>
      </trans-unit>
      <trans-unit id="2fe5beaf842ddac297ade8ef5ba379cc">
        <source>Feature (Name:Value:Position:Customized)</source>
        <target>Feature (Name:Value:Position:Customized)</target>
        <note>Line: 307</note>
      </trans-unit>
      <trans-unit id="446e04d1ed3b8fca04274d7c726a3fd5">
        <source>Available online only (0 = No, 1 = Yes)</source>
        <target>Available online only (0 = No, 1 = Yes)</target>
        <note>Line: 308</note>
      </trans-unit>
      <trans-unit id="f53c39de16dea1ee2352ab566702eb27">
        <source>Customizable (0 = No, 1 = Yes)</source>
        <target>Customizable (0 = No, 1 = Yes)</target>
        <note>Line: 310</note>
      </trans-unit>
      <trans-unit id="da505e5a6dd5678894aa9f8663bf4db0">
        <source>Uploadable files (0 = No, 1 = Yes)</source>
        <target>Uploadable files (0 = No, 1 = Yes)</target>
        <note>Line: 311</note>
      </trans-unit>
      <trans-unit id="6bfdfdf56a73c0726864489f78e4f91b">
        <source>Text fields (0 = No, 1 = Yes)</source>
        <target>Text fields (0 = No, 1 = Yes)</target>
        <note>Line: 312</note>
      </trans-unit>
      <trans-unit id="27cb4532e39ccfac2114be6223cf3534">
        <source>Action when out of stock</source>
        <target>Action when out of stock</target>
        <note>Line: 313</note>
      </trans-unit>
      <trans-unit id="81188f605f25cfc48952bb6b754cf69f">
        <source>Virtual product (0 = No, 1 = Yes)</source>
        <target>Virtual product (0 = No, 1 = Yes)</target>
        <note>Line: 314</note>
      </trans-unit>
      <trans-unit id="b3d2db69feecaedff30f1e0bc60206d6">
        <source>File URL</source>
        <target>File URL</target>
        <note>Line: 315</note>
      </trans-unit>
      <trans-unit id="8b155133fee28ec212ccf75a28e0711e">
        <source>Expiration date (yyyy-mm-dd)</source>
        <target>Expiration date (yyyy-mm-dd)</target>
        <note>Line: 320</note>
      </trans-unit>
      <trans-unit id="58fd2b2308056ad80255a322b305742b">
        <source>Number of days</source>
        <target>Number of days</target>
        <note>Line: 322</note>
      </trans-unit>
      <trans-unit id="63a293f69541c07a826ac0e8f35e6222">
        <source>Accessories (x,y,z...)</source>
        <target>Accessories (x,y,z...)</target>
        <note>Line: 341</note>
      </trans-unit>
      <trans-unit id="fd0dcc6233b026d257763713c133cf72">
        <source>Active (0/1)</source>
        <target>Active (0/1)</target>
        <note>Line: 525</note>
      </trans-unit>
      <trans-unit id="427b6d816d7fdd86cabe48d8180a3cc9">
        <source>Image URL</source>
        <target>Image URL</target>
        <note>Line: 540</note>
      </trans-unit>
      <trans-unit id="783cb853aae6984e51583b3bb80c09d2">
        <source>Address (2)</source>
        <target>Address (2)</target>
        <note>Line: 528</note>
      </trans-unit>
      <trans-unit id="3b0eb7469ba9c95f3a05c4cef1f6aac4">
        <source>Latitude</source>
        <target>Latitude</target>
        <note>Line: 533</note>
      </trans-unit>
      <trans-unit id="9b4dabc50f0b8ccba1c8981831abdad8">
        <source>Longitude</source>
        <target>Longitude</target>
        <note>Line: 534</note>
      </trans-unit>
      <trans-unit id="f42301fc8497aeb77b516f31b2d57d61">
        <source>Hours (x,y,z...)</source>
        <target>Hours (x,y,z...)</target>
        <note>Line: 539</note>
      </trans-unit>
      <trans-unit id="fad1595f2ba976ad028ec84cc57a56c2">
        <source>ID / Name of the store</source>
        <target>ID / Name of the store</target>
        <note>Line: 542</note>
      </trans-unit>
      <trans-unit id="3d1c10fcfac7da07fdb8b9f99d1c0901">
        <source>ID / Name of group shop</source>
        <target>ID / Name of group shop</target>
        <note>Line: 478</note>
      </trans-unit>
      <trans-unit id="b8495b50f5c172ebad1cc5c615548afa">
        <source>Ignore this column</source>
        <target>Ignore this column</target>
        <note>Line: 596</note>
      </trans-unit>
      <trans-unit id="6b127dc82937049e45e74aebca783e74">
        <source>Store contacts</source>
        <target>Store contacts</target>
        <note>Line: 4635</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminLoginController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="60e3ee398aa0ee3498c68914b5d5fb1a">
        <source>Back office connection from %ip%</source>
        <target>Back office connection from %ip%</target>
        <note>Line: 254</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminQuickAccessesController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="8ceefbf99a59faebc1abfe60004fa5cd">
        <source>%class_name% addition</source>
        <target>%class_name% addition</target>
        <note>Line: 184
Comment: voluntary do affectation here</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminRequestSqlController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="bbed49e90ee59ac450e80b492f452d0e">
        <source>Edit this SQL query</source>
        <target>Edit this SQL query</target>
        <note>Line: 108</note>
      </trans-unit>
      <trans-unit id="10d8074bf1e35bcac9911c5fe26261cf">
        <source>Select your default file encoding</source>
        <target>Select your default file encoding</target>
        <note>Line: 71</note>
      </trans-unit>
      <trans-unit id="9b5c3d6674b3af1946fb50faae7079e2">
        <source>SQL query name</source>
        <target>SQL query name</target>
        <note>Line: 153</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminShopController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="775a3ab6add326ef93f2382f49f9e500">
        <source>Attribute groups</source>
        <target>Attribute groups</target>
        <note>Line: 586</note>
      </trans-unit>
      <trans-unit id="3b8c8bf8ab2226d5a44454a20ad7fafb">
        <source>Cart rules</source>
        <target>Cart rules</target>
        <note>Line: 592</note>
      </trans-unit>
      <trans-unit id="5e3d6d040d8a81befd75127d526646bc">
        <source>Contact information</source>
        <target>Contact information</target>
        <note>Line: 569</note>
      </trans-unit>
      <trans-unit id="a66b5528ae08e780a5df59ae913c66e7">
        <source>Discount prices</source>
        <target>Discount prices</target>
        <note>Line: 572</note>
      </trans-unit>
      <trans-unit id="b71593973c58bb41eb302d037f6b1230">
        <source>Module hooks</source>
        <target>Module hooks</target>
        <note>Line: 578</note>
      </trans-unit>
      <trans-unit id="f71429893913cde74dcacde3d283626e">
        <source>Meta information</source>
        <target>Meta information</target>
        <note>Line: 579</note>
      </trans-unit>
      <trans-unit id="4ae24b703273d53941a17737bb2b1a9a">
        <source>Product combinations</source>
        <target>Product combinations</target>
        <note>Line: 581</note>
      </trans-unit>
      <trans-unit id="cf173b732a2a0377698d631db6185836">
        <source>Available quantities for sale</source>
        <target>Available quantities for sale</target>
        <note>Line: 582</note>
      </trans-unit>
      <trans-unit id="3020c78ae45aff9a35b95856af076765">
        <source>Warehouses</source>
        <target>Warehouses</target>
        <note>Line: 584</note>
      </trans-unit>
      <trans-unit id="339b1acb0d1f26923dc4545a9f749ab3">
        <source>Webservice accounts</source>
        <target>Webservice accounts</target>
        <note>Line: 585</note>
      </trans-unit>
      <trans-unit id="6fab199dbf5a4155871fb0d5dbfa9827">
        <source>Tax rules groups</source>
        <target>Tax rules groups</target>
        <note>Line: 589</note>
      </trans-unit>
      <trans-unit id="c8cfbbbe4253e14390b2b14d7e60d9c8">
        <source>Import data</source>
        <target>Import data</target>
        <note>Line: 609</note>
      </trans-unit>
      <trans-unit id="5fb93170a6b0ac85ca8c080d2c469702">
        <source>Choose the source shop</source>
        <target>Choose the source shop</target>
        <note>Line: 589</note>
      </trans-unit>
      <trans-unit id="1d92a46c4e14c43c3096d85c9f26fc4e">
        <source>Choose data to import</source>
        <target>Choose data to import</target>
        <note>Line: 624</note>
      </trans-unit>
      <trans-unit id="5a8f8f8e0bcbfcd926271ee7d7351134">
        <source>Shop groups list</source>
        <target>Shop groups list</target>
        <note>Line: 860</note>
      </trans-unit>
      <trans-unit id="80f6b81a57a2d0130fc0c998a03d3cf9">
        <source>Choose the source store</source>
        <target>Choose the source store</target>
        <note>Line: 616</note>
      </trans-unit>
      <trans-unit id="d38c99665bd92e632c7ad19e47d1222d">
        <source>Store groups list</source>
        <target>Store groups list</target>
        <note>Line: 892</note>
      </trans-unit>
      <trans-unit id="517a8a7dbf1f6e371509817de328e396">
        <source>Customer groups</source>
        <target>Customer groups</target>
        <note>Line: 588</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminShopGroupController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="4493e821e06072415518bd7ae4077996">
        <source>Shop group</source>
        <target>Shop group</target>
        <note>Line: 179</note>
      </trans-unit>
      <trans-unit id="90f72fb554403a6b1cb46741c5f348f2">
        <source>Multistore options</source>
        <target>Multistore options</target>
        <note>Line: 66</note>
      </trans-unit>
      <trans-unit id="8cdf1c6345219fcfde50615a78ad3ce6">
        <source>Default shop</source>
        <target>Default shop</target>
        <note>Line: 69</note>
      </trans-unit>
      <trans-unit id="d60f2e4b0fe028af01eb653ff90f7675">
        <source>Add a new shop group</source>
        <target>Add a new shop group</target>
        <note>Line: 169</note>
      </trans-unit>
      <trans-unit id="99dba47935ec26654ca99c21650779e6">
        <source>Add a new shop</source>
        <target>Add a new shop</target>
        <note>Line: 155</note>
      </trans-unit>
      <trans-unit id="07a415776bc6aefc538acd85f4d56730">
        <source>Shop group name</source>
        <target>Shop group name</target>
        <note>Line: 186</note>
      </trans-unit>
      <trans-unit id="5b55607ff39362e86c0693626a6cc20f">
        <source>Share customers</source>
        <target>Share customers</target>
        <note>Line: 206</note>
      </trans-unit>
      <trans-unit id="f11fe16f68bf58c9dde0f23156a5dd5d">
        <source>Share available quantities for sale</source>
        <target>Share available quantities for sale</target>
        <note>Line: 226</note>
      </trans-unit>
      <trans-unit id="166dcd2b34652eee355742934826795a">
        <source>Share available quantities between shops of this group. When changing this option, all available products quantities will be reset to 0.</source>
        <target>Share available quantities between shops of this group. When changing this option, all available products quantities will be reset to 0.</target>
        <note>Line: 241</note>
      </trans-unit>
      <trans-unit id="a23a8100bb34ea482c8fb134da89710e">
        <source>Share orders</source>
        <target>Share orders</target>
        <note>Line: 245</note>
      </trans-unit>
      <trans-unit id="e2dc533eb11f8dca38c806a5635533b6">
        <source>Store group</source>
        <target>Store group</target>
        <note>Line: 184</note>
      </trans-unit>
      <trans-unit id="43d180619356641995d3c1575c4bad12">
        <source>Default store</source>
        <target>Default store</target>
        <note>Line: 69</note>
      </trans-unit>
      <trans-unit id="2bf15871fc25294752b26d49fe93e49c">
        <source>Add a new group of stores</source>
        <target>Add a new group of stores</target>
        <note>Line: 169</note>
      </trans-unit>
      <trans-unit id="ecf1759c7e770af2c288ee6fd92777e6">
        <source>Add a new store</source>
        <target>Add a new store</target>
        <note>Line: 155</note>
      </trans-unit>
      <trans-unit id="f99969398eb4fb8f688a20bd5347fc05">
        <source>Name of the store group</source>
        <target>Name of the store group</target>
        <note>Line: 191</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminShopUrlController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="224154fa6f3601b7de4690ba6a9ad8b4">
        <source>Multistore tree</source>
        <target>Multistore tree</target>
        <note>Line: 382</note>
      </trans-unit>
      <trans-unit id="4a4d1260782270739eeeef6d58db0f73">
        <source>Shop URL ID</source>
        <target>Shop URL ID</target>
        <note>Line: 66</note>
      </trans-unit>
      <trans-unit id="e93c33bd1341ab74195430daeb63db13">
        <source>Shop name</source>
        <target>Shop name</target>
        <note>Line: 71</note>
      </trans-unit>
      <trans-unit id="71a7f085c9ecfd1ac1042c32c0656927">
        <source>Is it the main URL?</source>
        <target>Is it the main URL?</target>
        <note>Line: 81</note>
      </trans-unit>
      <trans-unit id="bfc70beddce99a8ab159563d4e03f7af">
        <source>URL options</source>
        <target>URL options</target>
        <note>Line: 164</note>
      </trans-unit>
      <trans-unit id="438ddd6a05ebb1af2b3e5dc794489f57">
        <source>Is it the main URL for this shop?</source>
        <target>Is it the main URL for this shop?</target>
        <note>Line: 167</note>
      </trans-unit>
      <trans-unit id="37d00f4554dc3c0baeb34a9c22d787d9">
        <source>Shop URL</source>
        <target>Shop URL</target>
        <note>Line: 220</note>
      </trans-unit>
      <trans-unit id="eae639a70006feff484a39363c977e24">
        <source>Domain</source>
        <target>Domain</target>
        <note>Line: 246</note>
      </trans-unit>
      <trans-unit id="f9f0dece476d9f76e4130347b6e31b94">
        <source>SSL Domain</source>
        <target>SSL Domain</target>
        <note>Line: 252</note>
      </trans-unit>
      <trans-unit id="4f04a71599485ace316aa8ed38348875">
        <source>Physical URL</source>
        <target>Physical URL</target>
        <note>Line: 258</note>
      </trans-unit>
      <trans-unit id="bbc1203755645168294af35f880d61e5">
        <source>Virtual URL</source>
        <target>Virtual URL</target>
        <note>Line: 265</note>
      </trans-unit>
      <trans-unit id="1dcec4396291e2fbdc02c03526fe2b88">
        <source>Final URL</source>
        <target>Final URL</target>
        <note>Line: 273</note>
      </trans-unit>
      <trans-unit id="d63c1ea9ab7850a74940ff760f25034b">
        <source>Edit this shop</source>
        <target>Edit this shop</target>
        <note>Line: 307</note>
      </trans-unit>
      <trans-unit id="157d64669ca816a7153ac30d2e16690a">
        <source>Add a new URL</source>
        <target>Add a new URL</target>
        <note>Line: 352</note>
      </trans-unit>
      <trans-unit id="6212233110ba622de7ad95ea86730a13">
        <source>Store URL ID</source>
        <target>Store URL ID</target>
        <note>Line: 66</note>
      </trans-unit>
      <trans-unit id="e72dca5d5a8a4706a206f3225324bf44">
        <source>Store name</source>
        <target>Store name</target>
        <note>Line: 71</note>
      </trans-unit>
      <trans-unit id="055898388116edd4234398bd11f142a9">
        <source>Is it the main URL for this store?</source>
        <target>Is it the main URL for this store?</target>
        <note>Line: 187</note>
      </trans-unit>
      <trans-unit id="2244fb2cc638a8f0d0b9630d51c14779">
        <source>Store URL</source>
        <target>Store URL</target>
        <note>Line: 240</note>
      </trans-unit>
      <trans-unit id="f22be75de278f6df8ec0fe9cf7a84b5f">
        <source>Edit this store</source>
        <target>Edit this store</target>
        <note>Line: 327</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/blockwishlist/src/Grid/Definition/BaseStatisticsGridDefinitionFactory.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="c33e404a441c6ba9648f88af3c68a1ca">
        <source>Statistics</source>
        <target>Statistics</target>
        <note>Line: 39</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/dashactivity/views/templates/hook/dashboard_zone_one.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a274f4d4670213a9045ce258c6c56b80">
        <source>Notifications</source>
        <target>Notifications</target>
        <note>Line: 101</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/statsnewsletter/statsnewsletter.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="130c5b3473c57faa76e2a1c54e26f88e">
        <source>Both</source>
        <target>Both</target>
        <note>Line: 125</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Adapter/Email/EmailConfigurationTester.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="94fc5d3be31c72a400c50aae395f1f1f">
        <source>This is a test message. Your server is now configured to send email.</source>
        <target>This is a test message. Your server is now configured to send email.</target>
        <note>Line: 73</note>
      </trans-unit>
      <trans-unit id="e379446940f88886aba80367a10fa4cc">
        <source>Test message -- Prestashop</source>
        <target>Test message -- Prestashop</target>
        <note>Line: 77</note>
      </trans-unit>
      <trans-unit id="4e6ebf3087084e1fda3554d154851ef0">
        <source>Error: Please check your configuration</source>
        <target>Error: Please check your configuration</target>
        <note>Line: 112</note>
      </trans-unit>
      <trans-unit id="a5e2754793644a64152395cd53ec3a30">
        <source>An error has occurred. Please check your configuration</source>
        <target>An error has occurred. Please check your configuration</target>
        <note>Line: 112</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Adapter/Requirement/CheckRequirements.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="15cfc4f0fa73c3f13280e47c5621a615">
        <source>%key% (missing description)</source>
        <target>%key% (missing description)</target>
        <note>Line: 143</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Form/ChoiceProvider/MailMethodChoiceProvider.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="f2fc8e9d7dc8852da27437d55be02453">
        <source>Use /usr/sbin/sendmail (recommended; works in most cases)</source>
        <target>Use /usr/sbin/sendmail (recommended; works in most cases)</target>
        <note>Line: 59</note>
      </trans-unit>
      <trans-unit id="e6095b01597caf275fb24fa766fb8a57">
        <source>Set my own SMTP parameters (for advanced users ONLY)</source>
        <target>Set my own SMTP parameters (for advanced users ONLY)</target>
        <note>Line: 63</note>
      </trans-unit>
      <trans-unit id="982bb3f2cc4922620fd3aee7ff15b552">
        <source>Never send emails (may be useful for testing purposes)</source>
        <target>Never send emails (may be useful for testing purposes)</target>
        <note>Line: 67</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Form/ChoiceProvider/PermissionsChoiceProvider.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="f1a627ad565dc779ed5a01edafd18640">
        <source>View (GET)</source>
        <target>View (GET)</target>
        <note>Line: 58</note>
      </trans-unit>
      <trans-unit id="9fd0670413556e6cbdc490e09833a30f">
        <source>Modify (PUT)</source>
        <target>Modify (PUT)</target>
        <note>Line: 59</note>
      </trans-unit>
      <trans-unit id="b28e80674d48866203b7790f730dfa30">
        <source>Add (POST)</source>
        <target>Add (POST)</target>
        <note>Line: 60</note>
      </trans-unit>
      <trans-unit id="be33a0b192776d871d8a235ede3523a8">
        <source>Patch (PATCH)</source>
        <target>Patch (PATCH)</target>
        <note>Line: 61</note>
      </trans-unit>
      <trans-unit id="6c0c2b9ec9a4188db4cfd4f8a0fee415">
        <source>Delete (DELETE)</source>
        <target>Delete (DELETE)</target>
        <note>Line: 62</note>
      </trans-unit>
      <trans-unit id="028f42454a71f86007a79664b2a35882">
        <source>Fast view (HEAD)</source>
        <target>Fast view (HEAD)</target>
        <note>Line: 63</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Grid/Definition/Factory/AttributeGroupGridDefinitionFactory.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="e129ae480280e47ef82fc7702f8321ba">
        <source>Refresh list</source>
        <target>Refresh list</target>
        <note>Line: 163</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Grid/Definition/Factory/BackupDefinitionFactory.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="9d8d2d5ab12b515182a505f54db7f538">
        <source>Age</source>
        <target>Age</target>
        <note>Line: 83</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Grid/Definition/Factory/EmailLogsDefinitionFactory.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="5d6103b662f41b07e10687f03aca8fdc">
        <source>Recipient</source>
        <target>Recipient</target>
        <note>Line: 111</note>
      </trans-unit>
      <trans-unit id="c7892ebbb139886662c6f2fc8c450710">
        <source>Subject</source>
        <target>Subject</target>
        <note>Line: 132</note>
      </trans-unit>
      <trans-unit id="7f8c0283f16925caed8e632086b81b9c">
        <source>Sent</source>
        <target>Sent</target>
        <note>Line: 139</note>
      </trans-unit>
      <trans-unit id="dcc459e0cef1e36a18a356fbc5789b16">
        <source>Erase all</source>
        <target>Erase all</target>
        <note>Line: 231</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Grid/Definition/Factory/LogGridDefinitionFactory.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="ecb833e2dc5d6c03d4d0cddf1b15e85a">
        <source>Severity (1-4)</source>
        <target>Severity (1-4)</target>
        <note>Line: 104</note>
      </trans-unit>
      <trans-unit id="0610bbb9dba03bb8037e468534b65a1a">
        <source>Object type</source>
        <target>Object type</target>
        <note>Line: 119</note>
      </trans-unit>
      <trans-unit id="d95fc016a6eee828f434ed5f55504427">
        <source>Object ID</source>
        <target>Object ID</target>
        <note>Line: 126</note>
      </trans-unit>
      <trans-unit id="5f36cf760a5c474cc32a6d35d9c50d05">
        <source>Error code</source>
        <target>Error code</target>
        <note>Line: 147</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Grid/Definition/Factory/Security/Session/EmployeeGridDefinitionFactory.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="236d0eb7130ba05580fa0cb21f6b968d">
        <source>Last update</source>
        <target>Last update</target>
        <note>Line: 145</note>
      </trans-unit>
      <trans-unit id="8c01c65cd2c7619b8bb617b441cd05c4">
        <source>The user will be signed out from this session.</source>
        <target>The user will be signed out from this session.</target>
        <note>Line: 164</note>
      </trans-unit>
      <trans-unit id="b49d9bafc2f2394cdd875fb0b96bb974">
        <source>Delete session?</source>
        <target>Delete session?</target>
        <note>Line: 170</note>
      </trans-unit>
      <trans-unit id="12e882347d5f51cf292ca08e2e606eb2">
        <source>Users will be signed out from all selected sessions.</source>
        <target>Users will be signed out from all selected sessions.</target>
        <note>Line: 293</note>
      </trans-unit>
      <trans-unit id="b0778bfd9332089cdb4468bd9f04e031">
        <source>Delete selected sessions?</source>
        <target>Delete selected sessions?</target>
        <note>Line: 299</note>
      </trans-unit>
      <trans-unit id="4d372ba3330d919603c6279404505f7c">
        <source>Employee ID</source>
        <target>Employee ID</target>
        <note>Line: 117</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Grid/Definition/Factory/WebserviceKeyDefinitionFactory.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="897356954c2cd3d41b221e3f24f99bba">
        <source>Key</source>
        <target>Key</target>
        <note>Line: 86</note>
      </trans-unit>
      <trans-unit id="52699c78843b98f98620186a59b0a3fa">
        <source>Key description</source>
        <target>Key description</target>
        <note>Line: 93</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Import/EntityField/Provider/CategoryFieldsProvider.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="f8a0fa3674c3336359b77bbe8e942a2c">
        <source>ID / Name of shop</source>
        <target>ID / Name of shop</target>
        <note>Line: 74</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Import/EntityField/Provider/CombinationFieldsProvider.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="2c703530391615c84509eab81dbff87c">
        <source>Advanced stock management</source>
        <target>Advanced stock management</target>
        <note>Line: 89</note>
      </trans-unit>
      <trans-unit id="75ed578ac3cb02b0ba40002a25bc0403">
        <source>Product reference</source>
        <target>Product reference</target>
        <note>Line: 58</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Import/EntityField/Provider/ProductFieldsProvider.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="52eb5928a34db3e3da7ba52b0644273b">
        <source>EAN13</source>
        <target>EAN13</target>
        <note>Line: 74</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Controller/Admin/Configure/AdvancedParameters/FeatureFlagController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="997320bab8342395326fd0a109068200">
        <source><![CDATA[New & Experimental Features]]></source>
        <target><![CDATA[New & Experimental Features]]></target>
        <note>Line: 96</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Controller/Admin/Configure/AdvancedParameters/ProfileController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="69d18853cb35672b765f55ae37ae4967">
        <source>Add new profile</source>
        <target>Add new profile</target>
        <note>Line: 149</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Controller/Api/StockController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="ae5977a08e8a3d45283789d99c67c3c3">
        <source>Combination reference</source>
        <target>Combination reference</target>
        <note>Line: 186</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/AdvancedParameters/Performance/CachingType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="bd74be2b0190f4b7f3ef117342771f70">
        <source>Memcached via PHP::Memcache</source>
        <target>Memcached via PHP::Memcache</target>
        <note>Line: 115</note>
      </trans-unit>
      <trans-unit id="e6f9c14400a56127e7397b4f8afeae10">
        <source>Memcached via PHP::Memcached</source>
        <target>Memcached via PHP::Memcached</target>
        <note>Line: 125</note>
      </trans-unit>
      <trans-unit id="0e68f753bc601f5d9d04717f95042762">
        <source>APC</source>
        <target>APC</target>
        <note>Line: 135</note>
      </trans-unit>
      <trans-unit id="38ca568bbe61771c6456a5c7b8d419ee">
        <source>Xcache</source>
        <target>Xcache</target>
        <note>Line: 145</note>
      </trans-unit>
      <trans-unit id="276e3171a63595e207ec292fce891277">
        <source>Use cache</source>
        <target>Use cache</target>
        <note>Line: 53</note>
      </trans-unit>
      <trans-unit id="aa7ef82bc75491903bfc4838957265be">
        <source>Caching system</source>
        <target>Caching system</target>
        <note>Line: 56</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/AdvancedParameters/Performance/CombineCompressCacheType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="c7ceaefb9d7fd3731ae2a28abad2cb95">
        <source>Smart cache for CSS</source>
        <target>Smart cache for CSS</target>
        <note>Line: 45</note>
      </trans-unit>
      <trans-unit id="cf00874a93cf1cca317853c54c9f40e3">
        <source>Smart cache for JavaScript</source>
        <target>Smart cache for JavaScript</target>
        <note>Line: 48</note>
      </trans-unit>
      <trans-unit id="0b388064aa29e8576990d9eb5da8d43e">
        <source>Apache optimization</source>
        <target>Apache optimization</target>
        <note>Line: 51</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/AdvancedParameters/Performance/DebugModeType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="03a9e6840847f96325b7184d5f3c2769">
        <source>Disable all overrides</source>
        <target>Disable all overrides</target>
        <note>Line: 46</note>
      </trans-unit>
      <trans-unit id="29826f9f4da3d473c1e4ecf2f6c4ef9f">
        <source>Enable or disable all classes and controllers overrides.</source>
        <target>Enable or disable all classes and controllers overrides.</target>
        <note>Line: 47</note>
      </trans-unit>
      <trans-unit id="1fa864793aa2394f509c5fa391826f0e">
        <source>Debug profiler</source>
        <target>Debug profiler</target>
        <note>Line: 56</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/AdvancedParameters/Performance/MediaServersType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="3ed7fddd0f381c4fe4683fc9e856dbcf">
        <source>Media server #1</source>
        <target>Media server #1</target>
        <note>Line: 45</note>
      </trans-unit>
      <trans-unit id="9dafd449b62301e26c635b976b231b88">
        <source>Media server #2</source>
        <target>Media server #2</target>
        <note>Line: 51</note>
      </trans-unit>
      <trans-unit id="391e79edab921e1974705a7096055dde">
        <source>Media server #3</source>
        <target>Media server #3</target>
        <note>Line: 57</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/AdvancedParameters/Performance/SmartyType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="79c0d6cba080dc90b01c887064c9fc2f">
        <source>Clear cache</source>
        <target>Clear cache</target>
        <note>Line: 76</note>
      </trans-unit>
      <trans-unit id="dbe2df5641344e1d14be812243f91ec5">
        <source>Never clear cache files</source>
        <target>Never clear cache files</target>
        <note>Line: 71</note>
      </trans-unit>
      <trans-unit id="8a9c2220c57773c2b05e4b800be3ba88">
        <source>Clear cache everytime something has been modified</source>
        <target>Clear cache everytime something has been modified</target>
        <note>Line: 72</note>
      </trans-unit>
      <trans-unit id="b8b65041a5ab3fa85c620238bbd3f390">
        <source>Never recompile template files</source>
        <target>Never recompile template files</target>
        <note>Line: 47</note>
      </trans-unit>
      <trans-unit id="a1d2406cf6a671bc87a3eb6b5db3a7fd">
        <source>Recompile templates if the files have been updated</source>
        <target>Recompile templates if the files have been updated</target>
        <note>Line: 48</note>
      </trans-unit>
      <trans-unit id="624718bb7cbf83ee3b5a78ecade12eac">
        <source>Force compilation</source>
        <target>Force compilation</target>
        <note>Line: 49</note>
      </trans-unit>
      <trans-unit id="770d85a02ce8841bd4dac97b57c7fa17">
        <source>Template compilation</source>
        <target>Template compilation</target>
        <note>Line: 53</note>
      </trans-unit>
      <trans-unit id="ab0cf104f39708eabd07b8cb67e149ba">
        <source>Cache</source>
        <target>Cache</target>
        <note>Line: 58</note>
      </trans-unit>
      <trans-unit id="0d6bf1934cab1de045b189eed03c3d42">
        <source>Should be enabled except for debugging.</source>
        <target>Should be enabled except for debugging.</target>
        <note>Line: 59</note>
      </trans-unit>
      <trans-unit id="1c9e9fdb43035d78fc94dd37c357869c">
        <source>Multi-front optimizations</source>
        <target>Multi-front optimizations</target>
        <note>Line: 63</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Administration/GeneralType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="dcfba1534995899d2ca36cda978da215">
        <source>Lifetime of front office cookies</source>
        <target>Lifetime of front office cookies</target>
        <note>Line: 54</note>
      </trans-unit>
      <trans-unit id="e62d77475fe6318731b4411ba1181dca">
        <source>Lifetime of back office cookies</source>
        <target>Lifetime of back office cookies</target>
        <note>Line: 59</note>
      </trans-unit>
      <trans-unit id="46f18d3960afc01e5a1a5a0e0e9d571b">
        <source>Automatically check for module updates</source>
        <target>Automatically check for module updates</target>
        <note>Line: 50</note>
      </trans-unit>
      <trans-unit id="967db4d257af07feced01d6633f575f7">
        <source>Check the cookie's IP address</source>
        <target>Check the cookie's IP address</target>
        <note>Line: 50</note>
      </trans-unit>
      <trans-unit id="57c6b2481e04c5d88b6b54e7b7167554">
        <source>Cookie SameSite</source>
        <target>Cookie SameSite</target>
        <note>Line: 64</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Administration/NotificationsType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="1b1befcb86d487715da458117710dfeb">
        <source>Show notifications for new orders</source>
        <target>Show notifications for new orders</target>
        <note>Line: 43</note>
      </trans-unit>
      <trans-unit id="8004e61ca76ff500d1e6ee92f7cb7f93">
        <source>Show notifications for new customers</source>
        <target>Show notifications for new customers</target>
        <note>Line: 46</note>
      </trans-unit>
      <trans-unit id="4e7ff7ca556a7ac8329ab27834e9631b">
        <source>Show notifications for new messages</source>
        <target>Show notifications for new messages</target>
        <note>Line: 49</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Administration/UploadQuotaType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="454036c51196464f8d9c84ded5808985">
        <source>Maximum size for attached files</source>
        <target>Maximum size for attached files</target>
        <note>Line: 67</note>
      </trans-unit>
      <trans-unit id="4ae386b852a3ee22324e8922e50c9aec">
        <source>Maximum size for a downloadable product</source>
        <target>Maximum size for a downloadable product</target>
        <note>Line: 99</note>
      </trans-unit>
      <trans-unit id="a1652ab7a761fa7bb3726fe28b97d1f3">
        <source>Maximum size for a product's image</source>
        <target>Maximum size for a product's image</target>
        <note>Line: 131</note>
      </trans-unit>
      <trans-unit id="2c111a587b8e6a65856ac7933d76bdce">
        <source>megabytes</source>
        <target>megabytes</target>
        <note>Line: 155</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Backup/BackupOptionsType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="6afc2b40f9acff2a4d1e67f2dfcd8a30">
        <source>Ignore statistics tables</source>
        <target>Ignore statistics tables</target>
        <note>Line: 85</note>
      </trans-unit>
      <trans-unit id="8859ec81a77f2f2b165bf5ea9858ecfc">
        <source>Drop existing tables during import</source>
        <target>Drop existing tables during import</target>
        <note>Line: 89</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Email/DkimConfigurationType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="fedcd10e34629cb88bd99a274dc7a023">
        <source>DKIM domain</source>
        <target>DKIM domain</target>
        <note>Line: 48</note>
      </trans-unit>
      <trans-unit id="cdb917a328e9b161bc66106a7df06cf6">
        <source>DKIM selector</source>
        <target>DKIM selector</target>
        <note>Line: 53</note>
      </trans-unit>
      <trans-unit id="df404822d04358870010b3d6dc407668">
        <source>DKIM private key</source>
        <target>DKIM private key</target>
        <note>Line: 59</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Email/EmailConfigurationType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="970e79a141128a95d3c2f35f622b6710">
        <source>Send email in HTML format</source>
        <target>Send email in HTML format</target>
        <note>Line: 98</note>
      </trans-unit>
      <trans-unit id="9f1b874f3886d361d6eb6b079639c385">
        <source>Send email in text format</source>
        <target>Send email in text format</target>
        <note>Line: 99</note>
      </trans-unit>
      <trans-unit id="c63600a5ac5af840103d0ee9608e7b44">
        <source>Log Emails</source>
        <target>Log Emails</target>
        <note>Line: 104</note>
      </trans-unit>
      <trans-unit id="29a45af04de19f488ed572e5ec04e8d1">
        <source>DKIM signing</source>
        <target>DKIM signing</target>
        <note>Line: 112</note>
      </trans-unit>
      <trans-unit id="c723ca5d9362fa1a05410d11c0bf86f2">
        <source>Enable the store name as a prefix in the email's subject</source>
        <target>Enable the store name as a prefix in the email's subject</target>
        <note>Line: 92</note>
      </trans-unit>
      <trans-unit id="ec76c5b544633d334c7a3f92db1c7efd">
        <source>Send emails to</source>
        <target>Send emails to</target>
        <note>Line: 77</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Email/SmtpConfigurationType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="6adf97f83acf6453d4a6a4b1070f3754">
        <source>None</source>
        <target>None</target>
        <note>Line: 75</note>
      </trans-unit>
      <trans-unit id="099d7d04319e5191b7473e016c55e320">
        <source>TLS</source>
        <target>TLS</target>
        <note>Line: 76</note>
      </trans-unit>
      <trans-unit id="ea52c36203c5f99c3ce2442d531b1a22">
        <source>SSL</source>
        <target>SSL</target>
        <note>Line: 77</note>
      </trans-unit>
      <trans-unit id="3e5b7324ee6019d543023eac3bb13016">
        <source>Email domain name</source>
        <target>Email domain name</target>
        <note>Line: 49</note>
      </trans-unit>
      <trans-unit id="bdd48fb41b9d0a4a1051fa22a87eb5a2">
        <source>SMTP server</source>
        <target>SMTP server</target>
        <note>Line: 54</note>
      </trans-unit>
      <trans-unit id="a7bfd8847270913b15396380c3627d76">
        <source>SMTP username</source>
        <target>SMTP username</target>
        <note>Line: 60</note>
      </trans-unit>
      <trans-unit id="3cffa2fabd5519beaf4c2fbd0610899b">
        <source>SMTP password</source>
        <target>SMTP password</target>
        <note>Line: 66</note>
      </trans-unit>
      <trans-unit id="d7f2615c71a1567cc13cf3a7f7de0aea">
        <source>Encryption</source>
        <target>Encryption</target>
        <note>Line: 80</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Email/TestEmailSendingType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="5f23f9739fa16efc7ddd06bb03096998">
        <source>Send a test email to</source>
        <target>Send a test email to</target>
        <note>Line: 46</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Employee/EmployeeOptionsType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="640fd0cc0ffa0316ae087652871f4486">
        <source>minutes</source>
        <target>minutes</target>
        <note>Line: 83</note>
      </trans-unit>
      <trans-unit id="67dc4a38907a53d52fb27cd3ae7972d6">
        <source>Password regeneration</source>
        <target>Password regeneration</target>
        <note>Line: 81</note>
      </trans-unit>
      <trans-unit id="4513dcc4cec4ce6e5a428c81ce7592ae">
        <source>Security: Minimum time to wait between two password changes.</source>
        <target>Security: Minimum time to wait between two password changes.</target>
        <note>Line: 85</note>
      </trans-unit>
      <trans-unit id="987f9e9654b8201f8a428d71d903b1d2">
        <source>Memorize the language used in Admin panel forms</source>
        <target>Memorize the language used in Admin panel forms</target>
        <note>Line: 91</note>
      </trans-unit>
      <trans-unit id="4696cdea33eee7e2c4fcb5dca30ccf56">
        <source>Allow employees to select a specific language for the Admin panel form.</source>
        <target>Allow employees to select a specific language for the Admin panel form.</target>
        <note>Line: 96</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Employee/EmployeeType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="57452e2dda1a10cfe2cc34c575e6b9d0">
        <source>Permission profile</source>
        <target>Permission profile</target>
        <note>Line: 210</note>
      </trans-unit>
      <trans-unit id="435a2d71a0c6c33cd2c917b00b8e0da1">
        <source>Default page</source>
        <target>Default page</target>
        <note>Line: 227</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Import/ImportDataConfigurationFormDataProvider.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="5f613cee6c941423b73e1f87ea3b99bc">
        <source>Please name your data matching configuration in order to save it.</source>
        <target>Please name your data matching configuration in order to save it.</target>
        <note>Line: 136</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Import/ImportType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="5489cb5630228b5afe0c21cc2ac9242c">
        <source>What do you want to import?</source>
        <target>What do you want to import?</target>
        <note>Line: 55</note>
      </trans-unit>
      <trans-unit id="14020ff11547c4c18da91394d1cae2a6">
        <source>Select a file to import</source>
        <target>Select a file to import</target>
        <note>Line: 73</note>
      </trans-unit>
      <trans-unit id="2a1a026f56b4c51cc839e513370379fd">
        <source>Language of the file</source>
        <target>Language of the file</target>
        <note>Line: 78</note>
      </trans-unit>
      <trans-unit id="35ebd0ba1ead54eee9c5684437aa519b">
        <source>Field separator</source>
        <target>Field separator</target>
        <note>Line: 82</note>
      </trans-unit>
      <trans-unit id="e08caeac3f80ffa7889cdf1c8f30111f">
        <source>Multiple value separator</source>
        <target>Multiple value separator</target>
        <note>Line: 86</note>
      </trans-unit>
      <trans-unit id="1269b023d7c37aceefb33ef49c9fec4d">
        <source>Delete all [1]categories[/1] before import</source>
        <target>Delete all [1]categories[/1] before import</target>
        <note>Line: 94</note>
      </trans-unit>
      <trans-unit id="feae9fd88b4d107b4de593e8b5a494d8">
        <source>Use product reference as key</source>
        <target>Use product reference as key</target>
        <note>Line: 106</note>
      </trans-unit>
      <trans-unit id="ff4ba5e49465c8bc4fb9ac41e57ae73d">
        <source>Skip thumbnails regeneration</source>
        <target>Skip thumbnails regeneration</target>
        <note>Line: 113</note>
      </trans-unit>
      <trans-unit id="0cbac2aa7274da1457a7c645202fcfbc">
        <source>Force all ID numbers</source>
        <target>Force all ID numbers</target>
        <note>Line: 119</note>
      </trans-unit>
      <trans-unit id="347ad906588aa29395f77470509165ec">
        <source>Send notification email</source>
        <target>Send notification email</target>
        <note>Line: 123</note>
      </trans-unit>
      <trans-unit id="c290a5941ad6eb0d5fe8752c15c5fd78">
        <source>Next step</source>
        <target>Next step</target>
        <note>Line: 127</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Logs/LogsByEmailType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="daf5a89c7110f00485d0737ff765e830">
        <source>Minimum severity level</source>
        <target>Minimum severity level</target>
        <note>Line: 52</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/RequestSql/SqlRequestSettingsType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="c1643cfb8e00b9f5e8cecabbac424f14">
        <source>Enable multi-statements queries</source>
        <target>Enable multi-statements queries</target>
        <note>Line: 52</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Security/GeneralType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a2ab0795ff399f2fbaccc3184a702c91">
        <source>Back office token protection</source>
        <target>Back office token protection</target>
        <note>Line: 48</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Security/PasswordPolicyType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="7c8eb7601cbe87bf48815729c2ee4264">
        <source>0 - Extremely guessable</source>
        <target>0 - Extremely guessable</target>
        <note>Line: 76</note>
      </trans-unit>
      <trans-unit id="3e1191ee418b9411aae871b276a3cda5">
        <source>1 - Very guessable</source>
        <target>1 - Very guessable</target>
        <note>Line: 77</note>
      </trans-unit>
      <trans-unit id="f81ccca9eb2a1dc7b9b3569174f4f8a6">
        <source>2 - Somewhat guessable</source>
        <target>2 - Somewhat guessable</target>
        <note>Line: 78</note>
      </trans-unit>
      <trans-unit id="38375da4832d4e3fc689d7bdd8797dac">
        <source>3 - Safely unguessable</source>
        <target>3 - Safely unguessable</target>
        <note>Line: 79</note>
      </trans-unit>
      <trans-unit id="8a86e2daedac9f0032313f7fb9b81db9">
        <source>4 - Very unguessable</source>
        <target>4 - Very unguessable</target>
        <note>Line: 80</note>
      </trans-unit>
      <trans-unit id="65be4bb9e294bfb3e8b7c09ca0017ef4">
        <source>Minimum length</source>
        <target>Minimum length</target>
        <note>Line: 50</note>
      </trans-unit>
      <trans-unit id="94f0e608658539187d65dddeea593677">
        <source>Maximum length</source>
        <target>Maximum length</target>
        <note>Line: 62</note>
      </trans-unit>
      <trans-unit id="2e2681ff9af0e6bdde9b775501adb368">
        <source>Minimum password security score</source>
        <target>Minimum password security score</target>
        <note>Line: 74</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Webservice/WebserviceConfigurationType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a3946b47f44a8f7adf9c9b6e38911790">
        <source>Enable PrestaShop's webservice</source>
        <target>Enable PrestaShop's webservice</target>
        <note>Line: 63</note>
      </trans-unit>
      <trans-unit id="8a05d87b51cc003b5a3c018b37a6d311">
        <source>Enable CGI mode for PHP</source>
        <target>Enable CGI mode for PHP</target>
        <note>Line: 72</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Webservice/WebserviceKeyType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="20eebdc752ee86d49f5d090759b237aa">
        <source>Webservice account key.</source>
        <target>Webservice account key.</target>
        <note>Line: 93</note>
      </trans-unit>
      <trans-unit id="8233506d8429d40687e2c8e051af63c7">
        <source>Enable webservice key</source>
        <target>Enable webservice key</target>
        <note>Line: 140</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Type/ChangePasswordType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="d9c2d86a66aa5a45326c3757f3a272cc">
        <source>Current password</source>
        <target>Current password</target>
        <note>Line: 78</note>
      </trans-unit>
      <trans-unit id="3544848f820b9d94a3f3871a382cf138">
        <source>New password</source>
        <target>New password</target>
        <note>Line: 88</note>
      </trans-unit>
      <trans-unit id="4c231e0da3eaaa6a9752174f7f9cfb31">
        <source>Confirm password</source>
        <target>Confirm password</target>
        <note>Line: 103</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Backup/Blocks/download_file.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="b3b5aea1ae3bf83287bde7e5295a22b4">
        <source>Download the backup file (%s MB)</source>
        <target>Download the backup file (%s MB)</target>
        <note>Line: 37</note>
      </trans-unit>
      <trans-unit id="0478391d3c5bfff8ce84cabb1ac330ee">
        <source>Tip: You can also download this file from your FTP server. Backup files are located in the "/adminXXXX/backups" directory.</source>
        <target>Tip: You can also download this file from your FTP server. Backup files are located in the "/adminXXXX/backups" directory.</target>
        <note>Line: 40</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Backup/Blocks/options.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="30c210e0173f2ff607cc84dc01ffc1f0">
        <source>Backup options</source>
        <target>Backup options</target>
        <note>Line: 33</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Blocks/import_available_fields.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="fdab87b387380c9bac4c542c90f1ce24">
        <source>Available fields</source>
        <target>Available fields</target>
        <note>Line: 30</note>
      </trans-unit>
      <trans-unit id="25a7ad3a7ab1d5e1f468448adf17f963">
        <source>* Required field</source>
        <target>* Required field</target>
        <note>Line: 42</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Blocks/import_file_history.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="c67947a1e18c763a4fc8e973caafacd8">
        <source>History of uploaded files</source>
        <target>History of uploaded files</target>
        <note>Line: 30</note>
      </trans-unit>
      <trans-unit id="801ab24683a4a8c433c6eb40c48bcd9d">
        <source>Download</source>
        <target>Download</target>
        <note>Line: 91</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Blocks/import_panel.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="b70c07744607bb44ce5a520716a7f8ed">
        <source>Choose from history / FTP</source>
        <target>Choose from history / FTP</target>
        <note>Line: 86</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Blocks/import_sample_files.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="8d97a849e6ac533d633f32fb829aa78c">
        <source>Download sample csv files</source>
        <target>Download sample csv files</target>
        <note>Line: 31</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Email/Blocks/email_logs_grid.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="86c9ce4b2e3b43353a23861c053a594b">
        <source>Back to the dashboard</source>
        <target>Back to the dashboard</target>
        <note>Line: 33</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Email/Blocks/test_email_sending.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a8a9e7c120e7020435d955d3a0c96471">
        <source>Test your email configuration</source>
        <target>Test your email configuration</target>
        <note>Line: 33</note>
      </trans-unit>
      <trans-unit id="0bb7ea7550d2e7caf5e32329bc7423cd">
        <source>A test email has been sent to the email address you provided.</source>
        <target>A test email has been sent to the email address you provided.</target>
        <note>Line: 40</note>
      </trans-unit>
      <trans-unit id="16186ead9220c2a4646402c69109cfa0">
        <source>Send a test email</source>
        <target>Send a test email</target>
        <note>Line: 49</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Employee/Blocks/employee_options.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a19df5d477865657e2e2d4f9c30325db">
        <source>Employee options</source>
        <target>Employee options</target>
        <note>Line: 34</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Employee/Blocks/form.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="eb626c94531ec554f93b2b78a77c8b1b">
        <source>Employees</source>
        <target>Employees</target>
        <note>Line: 34</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Employee/Blocks/showcase_card.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="0cb76cb9ab6f7b3a35122d6a3ef65da5">
        <source>Manage your team</source>
        <target>Manage your team</target>
        <note>Line: 32</note>
      </trans-unit>
      <trans-unit id="5d2ae622748ac5639d18bf235c196342">
        <source>Create profiles for your employees with specific rights. SuperAdmin can access the entire store while other people can only be dedicated to the catalog or orders pages.</source>
        <target>Create profiles for your employees with specific rights. SuperAdmin can access the entire store while other people can only be dedicated to the catalog or orders pages.</target>
        <note>Line: 33</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Employee/edit.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="04acbae6db9450374e9102fffd51d802">
        <source>Edit: %lastname% %firstname%</source>
        <target>Edit: %lastname% %firstname%</target>
        <note>Line: 29</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Employee/index.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="485263210a4a12e35e3426045e3301b1">
        <source>Add new employee</source>
        <target>Add new employee</target>
        <note>Line: 30</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/FeatureFlag/index.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="4078e029d86559a033707b1f7af7f04d">
        <source>New features</source>
        <target>New features</target>
        <note>Line: 37
Comment: Recommended modules will be attached to here.</note>
      </trans-unit>
      <trans-unit id="59c5b60130df8c2d91ce4e20f5a622bf">
        <source>Experimental features</source>
        <target>Experimental features</target>
        <note>Line: 65</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/ImportDataConfiguration/Blocks/import_data_configuration.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="43a37c093f7655efe882a7ffd7ce2e8d">
        <source>Match your data</source>
        <target>Match your data</target>
        <note>Line: 32</note>
      </trans-unit>
      <trans-unit id="e275f3b49dc3286ef66d7c2774a1f79b">
        <source>Please match each column of your source file to one of the destination columns.</source>
        <target>Please match each column of your source file to one of the destination columns.</target>
        <note>Line: 39</note>
      </trans-unit>
      <trans-unit id="4d83ebfaf28dd4436e1165a1764750db">
        <source>Load a data matching configuration</source>
        <target>Load a data matching configuration</target>
        <note>Line: 45</note>
      </trans-unit>
      <trans-unit id="e195639ebd7491676218b40af7aeafe3">
        <source>Save your data matching configuration</source>
        <target>Save your data matching configuration</target>
        <note>Line: 67</note>
      </trans-unit>
      <trans-unit id="3b057f0f92ac8d274701c269ac00c5fa">
        <source>Rows to skip</source>
        <target>Rows to skip</target>
        <note>Line: 85</note>
      </trans-unit>
      <trans-unit id="225ba2bdb1c56af16651110411fc21b1">
        <source>Indicate how many of the first rows of your file should be skipped when importing the data. For instance set it to 1 if the first row of your file contains headers.</source>
        <target>Indicate how many of the first rows of your file should be skipped when importing the data. For instance set it to 1 if the first row of your file contains headers.</target>
        <note>Line: 89</note>
      </trans-unit>
      <trans-unit id="371ed088d2c0e456f5cae0bb97fe7c4b">
        <source>Two columns cannot have the same type of values</source>
        <target>Two columns cannot have the same type of values</target>
        <note>Line: 95</note>
      </trans-unit>
      <trans-unit id="6a00e03023004b3330275e8d9842b54c">
        <source>This column must be set:</source>
        <target>This column must be set:</target>
        <note>Line: 100</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/LogsPage/Blocks/severity_levels.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="c9869fa659bd86f03aba826acd8db5b5">
        <source>Meaning of severity levels:</source>
        <target>Meaning of severity levels:</target>
        <note>Line: 32</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/LogsPage/index.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="5d1f62be1608c8c55ca77098efe5f053">
        <source>Logs by email</source>
        <target>Logs by email</target>
        <note>Line: 43</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Permission/Blocks/profile_tab_contents.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="82f23d77a3b5a362bc272bfc47d791ea">
        <source>No module</source>
        <target>No module</target>
        <note>Line: 112</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Profiles/Blocks/form.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="cce99c598cfdb9773ab041d54c3d973a">
        <source>Profile</source>
        <target>Profile</target>
        <note>Line: 32</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/RequestSql/Blocks/db_tables_panel.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="ac104da0cd80e52437b67aefa351e177">
        <source>List of MySQL Tables</source>
        <target>List of MySQL Tables</target>
        <note>Line: 37</note>
      </trans-unit>
      <trans-unit id="1ee79b4b20638d135b8ae8122f7770b9">
        <source>Please choose a table.</source>
        <target>Please choose a table.</target>
        <note>Line: 52</note>
      </trans-unit>
      <trans-unit id="1a696d8e9ac51e281e1cf1b646ddcc96">
        <source>Add table name to SQL query</source>
        <target>Add table name to SQL query</target>
        <note>Line: 54</note>
      </trans-unit>
      <trans-unit id="1938087a74c4d20f5454850ffbaf0e26">
        <source>List of attributes for this MySQL table</source>
        <target>List of attributes for this MySQL table</target>
        <note>Line: 62</note>
      </trans-unit>
      <trans-unit id="1141c5086ed62bf696bccf7de6b885bd">
        <source>Add attribute to SQL query</source>
        <target>Add attribute to SQL query</target>
        <note>Line: 65</note>
      </trans-unit>
      <trans-unit id="004bf6c9a40003140292e97330236c53">
        <source>Action</source>
        <target>Action</target>
        <note>Line: 70</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/RequestSql/Blocks/form.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="dddc702fcffceb2ac77291954897d747">
        <source>SQL query</source>
        <target>SQL query</target>
        <note>Line: 33</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/RequestSql/index.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="26420bd9e260012164a58a0f002e4821">
        <source>Add new SQL query</source>
        <target>Add new SQL query</target>
        <note>Line: 34</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/RequestSql/view.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="526a00f8a3c4c865b9c1834f38b5e2c6">
        <source>SQL query result</source>
        <target>SQL query result</target>
        <note>Line: 35</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Security/clear_form.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="4d943423c5b7a427d68555c0f971510a">
        <source>Outdated sessions</source>
        <target>Outdated sessions</target>
        <note>Line: 31</note>
      </trans-unit>
      <trans-unit id="2fabd0862b55cc866830e7c4fac43dbb">
        <source>Clear outdated sessions manually</source>
        <target>Clear outdated sessions manually</target>
        <note>Line: 53</note>
      </trans-unit>
      <trans-unit id="dc30bc0c7914db5918da4263fce93ad2">
        <source>Clear</source>
        <target>Clear</target>
        <note>Line: 59</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Security/index.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="6f2a85b1f1db5c398fe6123b80dcdb6b">
        <source>Password policy</source>
        <target>Password policy</target>
        <note>Line: 58</note>
      </trans-unit>
      <trans-unit id="8da7288a967251a5ece7398ee9ccc517">
        <source>0 means the password is extremely easy to guess (within 10^3 guesses). Dictionary words like "password" or "mother" score 0.</source>
        <target>0 means the password is extremely easy to guess (within 10^3 guesses). Dictionary words like "password" or "mother" score 0.</target>
        <note>Line: 69</note>
      </trans-unit>
      <trans-unit id="f6e312ff777ced3efe9ec2537ca94d80">
        <source>1 is still very easy to guess (guesses lesser than 10^6). An extra character on a dictionary word can score 1.</source>
        <target>1 is still very easy to guess (guesses lesser than 10^6). An extra character on a dictionary word can score 1.</target>
        <note>Line: 69</note>
      </trans-unit>
      <trans-unit id="8ff3208b2d25c53b96741f8e2a8caf92">
        <source>2 is pretty easy to guess (guesses lesser than 10^8). It provides some protection from unthrottled online attacks.</source>
        <target>2 is pretty easy to guess (guesses lesser than 10^8). It provides some protection from unthrottled online attacks.</target>
        <note>Line: 70</note>
      </trans-unit>
      <trans-unit id="e3f0680a29b17f58dad1ffc0c3692a58">
        <source>3 is safely unguessable (guesses lesser than 10^10). It offers moderate protection from offline slow-hash scenario.</source>
        <target>3 is safely unguessable (guesses lesser than 10^10). It offers moderate protection from offline slow-hash scenario.</target>
        <note>Line: 71</note>
      </trans-unit>
      <trans-unit id="9e4ed1a59cbfd96288f7e6376ddadf0c">
        <source>4 is very unguessable (guesses greater than or equal to 10^10) and provides strong protection from offline slow-hash scenario.</source>
        <target>4 is very unguessable (guesses greater than or equal to 10^10) and provides strong protection from offline slow-hash scenario.</target>
        <note>Line: 73</note>
      </trans-unit>
      <trans-unit id="478541a16f7d2800f3ea4427cb628ff0">
        <source>1 is still very easy to guess (guesses less than 10^6). An extra character on a dictionary word can score 1.</source>
        <target>1 is still very easy to guess (guesses less than 10^6). An extra character on a dictionary word can score 1.</target>
        <note>Line: 70</note>
      </trans-unit>
      <trans-unit id="1efea0fb945ffed57c73aec90dea4810">
        <source>2 is pretty easy to guess (guesses less than 10^8). It provides some protection from unthrottled online attacks.</source>
        <target>2 is pretty easy to guess (guesses less than 10^8). It provides some protection from unthrottled online attacks.</target>
        <note>Line: 71</note>
      </trans-unit>
      <trans-unit id="7355c93a5762175f1b68a5123e1234d7">
        <source>3 is safely unguessable (guesses less than 10^10). It offers moderate protection from offline slow-hash scenario.</source>
        <target>3 is safely unguessable (guesses less than 10^10). It offers moderate protection from offline slow-hash scenario.</target>
        <note>Line: 72</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Webservice/Blocks/form.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="58af3456f86b42c86f963d896a62644f">
        <source>Webservice Accounts</source>
        <target>Webservice Accounts</target>
        <note>Line: 32</note>
      </trans-unit>
      <trans-unit id="51ba9470e0c6fcc53b976a6854b0f2e0">
        <source>Set the resource permissions for this key:</source>
        <target>Set the resource permissions for this key:</target>
        <note>Line: 43</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Webservice/index.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="644ebe55744f361a22bc3c9be9bc4ccf">
        <source>Add new webservice key</source>
        <target>Add new webservice key</target>
        <note>Line: 34</note>
      </trans-unit>
      <trans-unit id="5a8228d4c852fd51c735b3563b33d3ed">
        <source>Webservice status</source>
        <target>Webservice status</target>
        <note>Line: 57</note>
      </trans-unit>
      <trans-unit id="2ab627ad288e54ed2712f137ceb37a37">
        <source>Webservice is enabled. Main entry point is</source>
        <target>Webservice is enabled. Main entry point is</target>
        <note>Line: 61</note>
      </trans-unit>
      <trans-unit id="ee67cd2d024d58dcd7fa82242c067479">
        <source>It seems that the webservice endpoint is not functional. If you are using httpd/apache2, you need to enable URL rewriting on your server.</source>
        <target>It seems that the webservice endpoint is not functional. If you are using httpd/apache2, you need to enable URL rewriting on your server.</target>
        <note>Line: 68</note>
      </trans-unit>
      <trans-unit id="72ba85f9d889729fb8ed9cf34c11688a">
        <source>Read the [1]developer documentation[/1].</source>
        <target>Read the [1]developer documentation[/1].</target>
        <note>Line: 72</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/administration.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="ade28d54bcdbc7c4cfd45d84ad517f7b">
        <source>Upload quota</source>
        <target>Upload quota</target>
        <note>Line: 58</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/memcache_servers.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="60aaf44d4b562252c04db7f98497e9aa">
        <source>Port</source>
        <target>Port</target>
        <note>Line: 56</note>
      </trans-unit>
      <trans-unit id="5b8c99dad1893a85076709b2d3c2d2d0">
        <source>IP Address</source>
        <target>IP Address</target>
        <note>Line: 55</note>
      </trans-unit>
      <trans-unit id="8c489d0946f66d17d73f26366a4bf620">
        <source>Weight</source>
        <target>Weight</target>
        <note>Line: 57</note>
      </trans-unit>
      <trans-unit id="74c9e12c3a19cfb79b79faf579934844">
        <source>Add server</source>
        <target>Add server</target>
        <note>Line: 35</note>
      </trans-unit>
      <trans-unit id="003744a685f896a2d9764655f67f1a24">
        <source>Add Server</source>
        <target>Add Server</target>
        <note>Line: 43</note>
      </trans-unit>
      <trans-unit id="dd11871dd215aec96a2eb051116bac00">
        <source>Test Server</source>
        <target>Test Server</target>
        <note>Line: 44</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/performance.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="ec3028a12402ab7f43962a6f3a667b6e">
        <source>Debug mode</source>
        <target>Debug mode</target>
        <note>Line: 63</note>
      </trans-unit>
      <trans-unit id="f8d698aea36fcbead2b9d5359ffca76f">
        <source>Smarty</source>
        <target>Smarty</target>
        <note>Line: 40</note>
      </trans-unit>
      <trans-unit id="bf17ac149e2e7a530c677e9bd51d3fd2">
        <source>Modules</source>
        <target>Modules</target>
        <note>Line: 85</note>
      </trans-unit>
      <trans-unit id="ae644e73ff5636d25f78961a75708b28">
        <source>Disable non built-in modules</source>
        <target>Disable non built-in modules</target>
        <note>Line: 103</note>
      </trans-unit>
      <trans-unit id="010a6cb0eccdea35662ad711d443f2ec">
        <source>Optional features</source>
        <target>Optional features</target>
        <note>Line: 130</note>
      </trans-unit>
      <trans-unit id="f4efdd180586cba038b52f2471e4075d">
        <source>CCC (Combine, Compress and Cache)</source>
        <target>CCC (Combine, Compress and Cache)</target>
        <note>Line: 162</note>
      </trans-unit>
      <trans-unit id="3ffcac2b857b421d5418b748b411265b">
        <source>Media servers (use only with CCC)</source>
        <target>Media servers (use only with CCC)</target>
        <note>Line: 194</note>
      </trans-unit>
      <trans-unit id="2d29e91503465ed72170623f37458e32">
        <source>You must enter another domain, or subdomain, in order to use cookieless static content.</source>
        <target>You must enter another domain, or subdomain, in order to use cookieless static content.</target>
        <note>Line: 201</note>
      </trans-unit>
      <trans-unit id="02d56cf7754ce16d7ce0193fbca7c90a">
        <source>Caching</source>
        <target>Caching</target>
        <note>Line: 226</note>
      </trans-unit>
      <trans-unit id="2fa165ecfa7ca3d29d17c598c7b267fc">
        <source>Disable all non-built-in modules?</source>
        <target>Disable all non-built-in modules?</target>
        <note>Line: 111</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/system_information.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="8b9a96e40ebcda35ce12190b41789b64">
        <source>Configuration information</source>
        <target>Configuration information</target>
        <note>Line: 33</note>
      </trans-unit>
      <trans-unit id="8ef8d8a1068d8d957f1e70c87cfa0fee">
        <source>This information must be provided when you report an issue on GitHub or on the forum.</source>
        <target>This information must be provided when you report an issue on GitHub or on the forum.</target>
        <note>Line: 37</note>
      </trans-unit>
      <trans-unit id="7d30d49dcf91d103c59496aafc4e3187">
        <source>Server information</source>
        <target>Server information</target>
        <note>Line: 42</note>
      </trans-unit>
      <trans-unit id="15ac02a8b5dba62592162905dd085241">
        <source>Server information:</source>
        <target>Server information:</target>
        <note>Line: 47</note>
      </trans-unit>
      <trans-unit id="ad41419235c76297eaf568c7656da881">
        <source>Server software version:</source>
        <target>Server software version:</target>
        <note>Line: 51</note>
      </trans-unit>
      <trans-unit id="e2551ec433c9623fed56f27ca2b91a35">
        <source>PHP version:</source>
        <target>PHP version:</target>
        <note>Line: 54</note>
      </trans-unit>
      <trans-unit id="94be595f109f75bfcced2d3bec366128">
        <source>Memory limit:</source>
        <target>Memory limit:</target>
        <note>Line: 57</note>
      </trans-unit>
      <trans-unit id="f3bd61801b2a43ad234af6e6fc917583">
        <source>Max execution time:</source>
        <target>Max execution time:</target>
        <note>Line: 60</note>
      </trans-unit>
      <trans-unit id="3d8a3c1f364b4767769b36eedbe66bb0">
        <source>Upload Max File size:</source>
        <target>Upload Max File size:</target>
        <note>Line: 63</note>
      </trans-unit>
      <trans-unit id="fc0eeb8415aed39a9d3624c7fc2fe47c">
        <source>PageSpeed module for Apache installed (mod_instaweb)</source>
        <target>PageSpeed module for Apache installed (mod_instaweb)</target>
        <note>Line: 66</note>
      </trans-unit>
      <trans-unit id="2933fa807adda5cc00e163479de35538">
        <source>Database information</source>
        <target>Database information</target>
        <note>Line: 73</note>
      </trans-unit>
      <trans-unit id="52018f4304c11e4bd288a533b122ccf7">
        <source>MySQL version:</source>
        <target>MySQL version:</target>
        <note>Line: 77</note>
      </trans-unit>
      <trans-unit id="dbaf046be46df36dab10f0a54e94b95e">
        <source>MySQL server:</source>
        <target>MySQL server:</target>
        <note>Line: 80</note>
      </trans-unit>
      <trans-unit id="a720577895f55e35dd9ea15feeadc35e">
        <source>MySQL name:</source>
        <target>MySQL name:</target>
        <note>Line: 83</note>
      </trans-unit>
      <trans-unit id="6088e183841f22493e40f574376240cb">
        <source>MySQL user:</source>
        <target>MySQL user:</target>
        <note>Line: 86</note>
      </trans-unit>
      <trans-unit id="fe453b675e37e3a3d2ca501d7ef3135e">
        <source>Tables prefix:</source>
        <target>Tables prefix:</target>
        <note>Line: 89</note>
      </trans-unit>
      <trans-unit id="b888bbc9841a225c7780b20f2c2db60c">
        <source>MySQL engine:</source>
        <target>MySQL engine:</target>
        <note>Line: 92</note>
      </trans-unit>
      <trans-unit id="9369ef07178c0e2be115d85fab6eb6cb">
        <source>MySQL driver:</source>
        <target>MySQL driver:</target>
        <note>Line: 95</note>
      </trans-unit>
      <trans-unit id="060925963c8da7da85dde6013ee4998b">
        <source>List of overrides</source>
        <target>List of overrides</target>
        <note>Line: 102</note>
      </trans-unit>
      <trans-unit id="3aea774cdcd8f2c45549f10758a71323">
        <source>Store information</source>
        <target>Store information</target>
        <note>Line: 122</note>
      </trans-unit>
      <trans-unit id="59681d7f729ee8ffdfbc99b8c935c41b">
        <source>PrestaShop version:</source>
        <target>PrestaShop version:</target>
        <note>Line: 126</note>
      </trans-unit>
      <trans-unit id="76c01eed328aa2411b18f007c77d2eeb">
        <source>Shop URL:</source>
        <target>Shop URL:</target>
        <note>Line: 129</note>
      </trans-unit>
      <trans-unit id="24c5ec8e15ae9467ee0f820610512d67">
        <source>Shop path:</source>
        <target>Shop path:</target>
        <note>Line: 132</note>
      </trans-unit>
      <trans-unit id="b5b3eed3efd6cfebfea270066a1e1f9b">
        <source>Current theme in use:</source>
        <target>Current theme in use:</target>
        <note>Line: 135</note>
      </trans-unit>
      <trans-unit id="4e37aa2987c0876f7f7b2104927df1a1">
        <source>Mail configuration</source>
        <target>Mail configuration</target>
        <note>Line: 142</note>
      </trans-unit>
      <trans-unit id="aee55e0ed7a06f68ed5e13f235a8bfd8">
        <source>Mail method:</source>
        <target>Mail method:</target>
        <note>Line: 151</note>
      </trans-unit>
      <trans-unit id="0398efcdf22e008d4ab53cc6eeab7a71">
        <source>You are using /usr/sbin/sendmail</source>
        <target>You are using /usr/sbin/sendmail</target>
        <note>Line: 147</note>
      </trans-unit>
      <trans-unit id="cffa72aaebae1bd90bbe1b8d827ecb1c">
        <source>You are using your own SMTP parameters.</source>
        <target>You are using your own SMTP parameters.</target>
        <note>Line: 151</note>
      </trans-unit>
      <trans-unit id="33b3f142e242a624cb69a5f5f689039d">
        <source>SMTP server:</source>
        <target>SMTP server:</target>
        <note>Line: 154</note>
      </trans-unit>
      <trans-unit id="5489d25adfc42d3aadcb90b679405bda">
        <source>SMTP username:</source>
        <target>SMTP username:</target>
        <note>Line: 157</note>
      </trans-unit>
      <trans-unit id="c4e9522d7b3c8c652f7f0333ff436eec">
        <source>Defined</source>
        <target>Defined</target>
        <note>Line: 167</note>
      </trans-unit>
      <trans-unit id="f8b1369a8e9d90da0cae0b11049309af">
        <source>Not defined</source>
        <target>Not defined</target>
        <note>Line: 169</note>
      </trans-unit>
      <trans-unit id="037373672dd4a89426144d40f2e8ad91">
        <source>SMTP password:</source>
        <target>SMTP password:</target>
        <note>Line: 165</note>
      </trans-unit>
      <trans-unit id="8a7363b823dce00b3b1b7e62ca1d777d">
        <source>Encryption:</source>
        <target>Encryption:</target>
        <note>Line: 173</note>
      </trans-unit>
      <trans-unit id="599303a627bf19148eedd5fe29112729">
        <source>SMTP port:</source>
        <target>SMTP port:</target>
        <note>Line: 176</note>
      </trans-unit>
      <trans-unit id="8746097684bc64be8b7eff424c4debdb">
        <source>Your information</source>
        <target>Your information</target>
        <note>Line: 184</note>
      </trans-unit>
      <trans-unit id="11eb3e792866d1433dd4ca9d7e49b5ac">
        <source>Your web browser:</source>
        <target>Your web browser:</target>
        <note>Line: 188</note>
      </trans-unit>
      <trans-unit id="e91dd9bbca6e42bb85c0f2d94aaee995">
        <source>Check your configuration</source>
        <target>Check your configuration</target>
        <note>Line: 195</note>
      </trans-unit>
      <trans-unit id="a3810ddb5a67e550cb3ca9c1cffce800">
        <source>Required parameters:</source>
        <target>Required parameters:</target>
        <note>Line: 205</note>
      </trans-unit>
      <trans-unit id="0297eee5a43818dda6ad1e1b36f08965">
        <source>Optional parameters:</source>
        <target>Optional parameters:</target>
        <note>Line: 224</note>
      </trans-unit>
      <trans-unit id="d5c0bb5c1df152c6fe45bd07e303cb69">
        <source>List of changed files</source>
        <target>List of changed files</target>
        <note>Line: 243</note>
      </trans-unit>
      <trans-unit id="5f1e07304be1553f59d16379fdc9a997">
        <source>You must provide this information when reporting an issue on GitHub or on the forum.</source>
        <target>You must provide this information when reporting an issue on GitHub or on the forum.</target>
        <note>Line: 36</note>
      </trans-unit>
      <trans-unit id="4553c4c7dae513827f77f2cfd01982c7">
        <source>No overrides have been found.</source>
        <target>No overrides have been found.</target>
        <note>Line: 107</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Helpers/password_feedback.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="43775ce1723057d9e3d21e5969324fd1">
        <source>Straight rows of keys are easy to guess</source>
        <target>Straight rows of keys are easy to guess</target>
        <note>Line: 32</note>
      </trans-unit>
      <trans-unit id="bed5e315d09dedac517831b95571a7f4">
        <source>Short keyboard patterns are easy to guess</source>
        <target>Short keyboard patterns are easy to guess</target>
        <note>Line: 33</note>
      </trans-unit>
      <trans-unit id="37ffb1976d12530b5c07821baaf5be39">
        <source>Use a longer keyboard pattern with more turns</source>
        <target>Use a longer keyboard pattern with more turns</target>
        <note>Line: 34</note>
      </trans-unit>
      <trans-unit id="773ac0d662987369abf46eebc9000a09">
        <source>Repeats like "aaa" are easy to guess</source>
        <target>Repeats like "aaa" are easy to guess</target>
        <note>Line: 35</note>
      </trans-unit>
      <trans-unit id="97681c90722711e8081dcbd10620502e">
        <source>Repeats like "abcabcabc" are only slightly harder to guess than "abc"</source>
        <target>Repeats like "abcabcabc" are only slightly harder to guess than "abc"</target>
        <note>Line: 36</note>
      </trans-unit>
      <trans-unit id="c0072984cd1f6cb90856cd8f00a4ad3b">
        <source>Sequences like abc or 6543 are easy to guess</source>
        <target>Sequences like abc or 6543 are easy to guess</target>
        <note>Line: 37</note>
      </trans-unit>
      <trans-unit id="25096ab4fbb9994547ace751ca107515">
        <source>Recent years are easy to guess</source>
        <target>Recent years are easy to guess</target>
        <note>Line: 38</note>
      </trans-unit>
      <trans-unit id="b3c7b778a3c8c869426ae81486275b12">
        <source>Dates are often easy to guess</source>
        <target>Dates are often easy to guess</target>
        <note>Line: 39</note>
      </trans-unit>
      <trans-unit id="954a21d373e3f1bd2446606700a37217">
        <source>This is a top-10 common password</source>
        <target>This is a top-10 common password</target>
        <note>Line: 40</note>
      </trans-unit>
      <trans-unit id="f6e52aa06b58dd0d8855340b165c0b49">
        <source>This is a top-100 common password</source>
        <target>This is a top-100 common password</target>
        <note>Line: 41</note>
      </trans-unit>
      <trans-unit id="0f4a64d2fecd6b4ab2c10d21313e02a6">
        <source>This is a very common password</source>
        <target>This is a very common password</target>
        <note>Line: 42</note>
      </trans-unit>
      <trans-unit id="c92578d4545d5aceb430aed0c7285398">
        <source>This is similar to a commonly used password</source>
        <target>This is similar to a commonly used password</target>
        <note>Line: 43</note>
      </trans-unit>
      <trans-unit id="9769bc6be635c708fee72d29f5a08f30">
        <source>A word by itself is easy to guess</source>
        <target>A word by itself is easy to guess</target>
        <note>Line: 44</note>
      </trans-unit>
      <trans-unit id="83812066e07a07e691d165c97ba89735">
        <source>Names and surnames by themselves are easy to guess</source>
        <target>Names and surnames by themselves are easy to guess</target>
        <note>Line: 45</note>
      </trans-unit>
      <trans-unit id="c8a17899dc267baa655e66e93dfa97a2">
        <source>Common names and surnames are easy to guess</source>
        <target>Common names and surnames are easy to guess</target>
        <note>Line: 46</note>
      </trans-unit>
      <trans-unit id="30a3534b67a9a2c4cb3f1bb8659b321f">
        <source>Very weak</source>
        <target>Very weak</target>
        <note>Line: 47</note>
      </trans-unit>
      <trans-unit id="7324e3727807d95037eb19d304fd91ec">
        <source>Weak</source>
        <target>Weak</target>
        <note>Line: 48</note>
      </trans-unit>
      <trans-unit id="b1897515d548a960afe49ecf66a29021">
        <source>Average</source>
        <target>Average</target>
        <note>Line: 49</note>
      </trans-unit>
      <trans-unit id="c43e0fd449c758dab8f891d8e19eb1a9">
        <source>Strong</source>
        <target>Strong</target>
        <note>Line: 50</note>
      </trans-unit>
      <trans-unit id="516b34a4d2351006f3a0f39ccffccefa">
        <source>Very strong</source>
        <target>Very strong</target>
        <note>Line: 51</note>
      </trans-unit>
      <trans-unit id="dd96380694cf9e241cba2b2ac4cf8c03">
        <source>Use a few words, avoid common phrases</source>
        <target>Use a few words, avoid common phrases</target>
        <note>Line: 52</note>
      </trans-unit>
      <trans-unit id="db3fe1774e30b4103c663805d999043b">
        <source>No need for symbols, digits, or uppercase letters</source>
        <target>No need for symbols, digits, or uppercase letters</target>
        <note>Line: 53</note>
      </trans-unit>
      <trans-unit id="6af81f974e7c0a35a3a80db30e8e9fa1">
        <source>Avoid repeated words and characters</source>
        <target>Avoid repeated words and characters</target>
        <note>Line: 54</note>
      </trans-unit>
      <trans-unit id="cf80cfbbf9eb1b8fc60a7c2c4a1d02d2">
        <source>Avoid sequences</source>
        <target>Avoid sequences</target>
        <note>Line: 55</note>
      </trans-unit>
      <trans-unit id="b4535b0e01e0d48c4e38758a933870ad">
        <source>Avoid recent years</source>
        <target>Avoid recent years</target>
        <note>Line: 56</note>
      </trans-unit>
      <trans-unit id="18d5eb58e70730c3d43d6a8775201057">
        <source>Avoid years that are associated with you</source>
        <target>Avoid years that are associated with you</target>
        <note>Line: 57</note>
      </trans-unit>
      <trans-unit id="54e26a80f66b734f94bc2bd91f3a39ca">
        <source>Avoid dates and years that are associated with you</source>
        <target>Avoid dates and years that are associated with you</target>
        <note>Line: 58</note>
      </trans-unit>
      <trans-unit id="fb6fb2ced4938c895874cd10f135a624">
        <source>Capitalization doesn't help very much</source>
        <target>Capitalization doesn't help very much</target>
        <note>Line: 59</note>
      </trans-unit>
      <trans-unit id="82958461f3498cc00c0e95ffc05c482d">
        <source>All-uppercase is almost as easy to guess as all-lowercase</source>
        <target>All-uppercase is almost as easy to guess as all-lowercase</target>
        <note>Line: 60</note>
      </trans-unit>
      <trans-unit id="fc51ea2f3761abe2201b06a33f75748e">
        <source>Reversed words aren't much harder to guess</source>
        <target>Reversed words aren't much harder to guess</target>
        <note>Line: 61</note>
      </trans-unit>
      <trans-unit id="e3c220047c05727a174783f106b69f2b">
        <source>Predictable substitutions like '@' instead of 'a' don't help very much</source>
        <target>Predictable substitutions like '@' instead of 'a' don't help very much</target>
        <note>Line: 62</note>
      </trans-unit>
      <trans-unit id="a2bfda43aef35df5f7edb8f8de1f5f30">
        <source>Add another word or two. Uncommon words are better.</source>
        <target>Add another word or two. Uncommon words are better.</target>
        <note>Line: 63</note>
      </trans-unit>
      <trans-unit id="b482b125161bc9e84d12df83251ebf2d">
        <source>Enter a password between %d and %d characters</source>
        <target>Enter a password between %d and %d characters</target>
        <note>Line: 73</note>
      </trans-unit>
      <trans-unit id="f43333ea2272fed37fc121a2e09755c3">
        <source>The minimum score must be: %s</source>
        <target>The minimum score must be: %s</target>
        <note>Line: 77</note>
      </trans-unit>
      <trans-unit id="de3492070cd44f280a6f398dca2f1efb">
        <source>Sequences like "abc" or "6543" are easy to guess</source>
        <target>Sequences like "abc" or "6543" are easy to guess</target>
        <note>Line: 37</note>
      </trans-unit>
      <trans-unit id="8aedfe9265263408f5a72fff0a21c118">
        <source>Predictable substitutions like "@" instead of "a" don't help very much</source>
        <target>Predictable substitutions like "@" instead of "a" don't help very much</target>
        <note>Line: 62</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Sell/Catalog/Manufacturer/logo_image.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="1908624a0bca678cd26b99bfd405324e">
        <source>File size</source>
        <target>File size</target>
        <note>Line: 28</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Sell/Order/Order/Blocks/View/Modal/add_product_modal.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="88627291a92931577d427c5fd68f511e">
        <source>Price tax excluded</source>
        <target>Price tax excluded</target>
        <note>Line: 58</note>
      </trans-unit>
      <trans-unit id="f154d2146a2acf439d955fea9d396ec2">
        <source>Price tax included</source>
        <target>Price tax included</target>
        <note>Line: 72</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Sell/Order/Order/Blocks/View/products.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="9d20a93036ba910dd75ffbcec173798e">
        <source>Price per unit</source>
        <target>Price per unit</target>
        <note>Line: 69</note>
      </trans-unit>
    </body>
  </file>
</xliff>
