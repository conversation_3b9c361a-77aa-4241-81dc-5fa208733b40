<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file original="classes/lang/KeysReference/FeatureFlagLang.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="37ab5632ec67e80fb6778e6dd6e6bdec">
        <source>This page benefits from increased performance and includes new features such as a new combination management system.</source>
        <target>This page benefits from increased performance and includes new features such as a new combination management system.</target>
        <note>Line: 38</note>
      </trans-unit>
      <trans-unit id="8d5b22a3a4421322149a37aba0ece176">
        <source>Access the new product page, even in a multistore context. This is a work in progress and some features are not available.</source>
        <target>Access the new product page, even in a multistore context. This is a work in progress and some features are not available.</target>
        <note>Line: 42</note>
      </trans-unit>
      <trans-unit id="b5c5b3a0a7208392aaf2c4d23a81879c">
        <source>This page benefits from increased performance and includes new features such as a new combination management system. Please note this is a work in progress and some features are not available yet.</source>
        <target>This page benefits from increased performance and includes new features such as a new combination management system. Please note this is a work in progress and some features are not available yet.</target>
        <note>Line: 30</note>
      </trans-unit>
      <trans-unit id="a536359574c27ee963de828ff56241c7">
        <source>Enable or disable the migrated attribute page.</source>
        <target>Enable or disable the migrated attribute page.</target>
        <note>Line: 46</note>
      </trans-unit>
      <trans-unit id="51efb19669aecfbc211f5b708170db38">
        <source>Enable or disable the authorization server page.</source>
        <target>Enable or disable the authorization server page.</target>
        <note>Line: 50</note>
      </trans-unit>
      <trans-unit id="f86c7bed82286a17b1e557d7235ed4a0">
        <source>Enable or disable the migrated cart rules page.</source>
        <target>Enable or disable the migrated cart rules page.</target>
        <note>Line: 54</note>
      </trans-unit>
      <trans-unit id="17e98e038d333c2d89bc59c843414298">
        <source>Enable or disable the migrated catalog price rules page.</source>
        <target>Enable or disable the migrated catalog price rules page.</target>
        <note>Line: 58</note>
      </trans-unit>
      <trans-unit id="dec852ff965efbef1b33366c7b927acd">
        <source>Enable or disable the migrated countries page.</source>
        <target>Enable or disable the migrated countries page.</target>
        <note>Line: 65</note>
      </trans-unit>
      <trans-unit id="21686cbcfb36e6039ba2f0af0909b05b">
        <source>Enable or disable the migrated states page.</source>
        <target>Enable or disable the migrated states page.</target>
        <note>Line: 69</note>
      </trans-unit>
      <trans-unit id="8437c82aa6a57b80f6956352690d7e47">
        <source>Enable or disable the migrated carriers page.</source>
        <target>Enable or disable the migrated carriers page.</target>
        <note>Line: 73</note>
      </trans-unit>
      <trans-unit id="375f32d57378784ca73de6ae649adf60">
        <source>Enable or disable the migrated titles page.</source>
        <target>Enable or disable the migrated titles page.</target>
        <note>Line: 77</note>
      </trans-unit>
      <trans-unit id="e93e9e56b71f065655b06fd7b069f181">
        <source>Enable or disable the migrated permissions page.</source>
        <target>Enable or disable the migrated permissions page.</target>
        <note>Line: 81</note>
      </trans-unit>
      <trans-unit id="acf9650ec7716ce837f6865d0dcc289b">
        <source>Enable or disable the migrated tax rules page.</source>
        <target>Enable or disable the migrated tax rules page.</target>
        <note>Line: 85</note>
      </trans-unit>
      <trans-unit id="4c225d5d1f44741cfa6e05837c40d4e3">
        <source>Enable or disable the migrated customer threads page.</source>
        <target>Enable or disable the migrated customer threads page.</target>
        <note>Line: 89</note>
      </trans-unit>
      <trans-unit id="a7980eefb5f08f8f3555df73e3dfa63f">
        <source>Enable or disable the migrated order states page.</source>
        <target>Enable or disable the migrated order states page.</target>
        <note>Line: 93</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminImportController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="e81c4e4f2b7b93b481e13a8553c2ae1b">
        <source>or</source>
        <target>or</target>
        <note>Line: 1074
Comment: Special case for Product : either one or the other. Not both.</note>
      </trans-unit>
      <trans-unit id="4bc14a0f722453ba235bc3fb52d95ffb">
        <source>A category root is where a category tree can begin. This is used with multistore.</source>
        <target>A category root is where a category tree can begin. This is used with multistore.</target>
        <note>Line: 212</note>
      </trans-unit>
      <trans-unit id="2d743ae4c74db2a22d79e1e950e719e5">
        <source>0 = Use quantity set in product, 1 = Use quantity from warehouse.</source>
        <target>0 = Use quantity set in product, 1 = Use quantity from warehouse.</target>
        <note>Line: 335</note>
      </trans-unit>
      <trans-unit id="aaf88088b0ac3f2992888fdfe00b5d76">
        <source>ID of the warehouse to set as storage.</source>
        <target>ID of the warehouse to set as storage.</target>
        <note>Line: 339</note>
      </trans-unit>
      <trans-unit id="3fc09360fdbfd34338847f0c4d2c706a">
        <source>Enable advanced stock management on product (0 = No, 1 = Yes)</source>
        <target>Enable advanced stock management on product (0 = No, 1 = Yes)</target>
        <note>Line: 169</note>
      </trans-unit>
      <trans-unit id="5ed37ab0d176038cd3289e1fe4fb5637">
        <source>Enable advanced stock management on product (0 = No, 1 = Yes).</source>
        <target>Enable advanced stock management on product (0 = No, 1 = Yes).</target>
        <note>Line: 331</note>
      </trans-unit>
      <trans-unit id="d2beb087630a099f2fada85e7f2f19fb">
        <source>Ignore this field if you don't use the Multistore tool. If you leave this field empty, the default store will be used.</source>
        <target>Ignore this field if you don't use the Multistore tool. If you leave this field empty, the default store will be used.</target>
        <note>Line: 543</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminShopController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="c127560c7252928dc38d9a0ebf312ba4">
        <source>Use this option to associate data (products, modules, etc.) the same way for each selected shop.</source>
        <target>Use this option to associate data (products, modules, etc.) the same way for each selected shop.</target>
        <note>Line: 627</note>
      </trans-unit>
      <trans-unit id="41ff4b3ea277bdc597fce74850a85510">
        <source>Click here to display the shops in the %name% shop group</source>
        <target>Click here to display the shops in the %name% shop group</target>
        <note>Line: 822</note>
      </trans-unit>
      <trans-unit id="d916f5b0696f10712fad55220707fab9">
        <source>Click here to display the URLs of the %name% shop</source>
        <target>Click here to display the URLs of the %name% shop</target>
        <note>Line: 844</note>
      </trans-unit>
      <trans-unit id="01af4d9c4808ce3ccd662c0e5ef7f203">
        <source>Click here to display the list of shop groups</source>
        <target>Click here to display the list of shop groups</target>
        <note>Line: 896</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminShopGroupController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="0c2afe3d3eb02183ee182aa447608bc4">
        <source>Warning: Enabling the "share customers" and "share orders" options is not recommended. Once activated and orders are created, you will not be able to disable these options. If you need these options, we recommend using several categories rather than several shops.</source>
        <target>Warning: Enabling the "share customers" and "share orders" options is not recommended. Once activated and orders are created, you will not be able to disable these options. If you need these options, we recommend using several categories rather than several shops.</target>
        <note>Line: 187</note>
      </trans-unit>
      <trans-unit id="ee5d0efc6b5d41f7ee2ac5ea831f2a7f">
        <source>Once this option is enabled, the shops in this group will share customers. If a customer registers in any one of these shops, the account will automatically be available in the others shops of this group.</source>
        <target>Once this option is enabled, the shops in this group will share customers. If a customer registers in any one of these shops, the account will automatically be available in the others shops of this group.</target>
        <note>Line: 222</note>
      </trans-unit>
      <trans-unit id="dace58c348a19d7bddb7a3fe4fd3ebfa">
        <source>Warning: you will not be able to disable this option once you have registered customers.</source>
        <target>Warning: you will not be able to disable this option once you have registered customers.</target>
        <note>Line: 222</note>
      </trans-unit>
      <trans-unit id="059a6f0b1a0a28d15ee51fe80670f6f2">
        <source>Once this option is enabled (which is only possible if customers and available quantities are shared among shops), the customer's cart will be shared by all shops in this group. This way, any purchase started in one shop will be able to be completed in another shop from the same group.</source>
        <target>Once this option is enabled (which is only possible if customers and available quantities are shared among shops), the customer's cart will be shared by all shops in this group. This way, any purchase started in one shop will be able to be completed in another shop from the same group.</target>
        <note>Line: 261</note>
      </trans-unit>
      <trans-unit id="697ce708715d1f771177623968e8c6a4">
        <source>Warning: You will not be able to disable this option once you've started to accept orders.</source>
        <target>Warning: You will not be able to disable this option once you've started to accept orders.</target>
        <note>Line: 261</note>
      </trans-unit>
      <trans-unit id="e667b16af30e0820218ad32242e284dc">
        <source>Enable or disable this shop group?</source>
        <target>Enable or disable this shop group?</target>
        <note>Line: 280</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminShopUrlController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="c688fbbc9468ea3473172ca924290860">
        <source>If you want to add a virtual URL, you need to activate URL rewriting on your web server and enable Friendly URL option.</source>
        <target>If you want to add a virtual URL, you need to activate URL rewriting on your web server and enable Friendly URL option.</target>
        <note>Line: 152</note>
      </trans-unit>
      <trans-unit id="05f8f0fab3ff5d86e0de9db902da7045">
        <source>You can use this option if you want to create a store with a URL that doesn't exist on your server (e.g. if you want your store to be available with the URL www.example.com/my-store/shoes/, you have to set shoes/ in this field, assuming that my-store/ is your Physical URL).</source>
        <target>You can use this option if you want to create a store with a URL that doesn't exist on your server (e.g. if you want your store to be available with the URL www.example.com/my-store/shoes/, you have to set shoes/ in this field, assuming that my-store/ is your Physical URL).</target>
        <note>Line: 156</note>
      </trans-unit>
      <trans-unit id="f6e7829277b67d5a8805d6728a810362">
        <source>URL rewriting must be activated on your server to use this feature.</source>
        <target>URL rewriting must be activated on your server to use this feature.</target>
        <note>Line: 157</note>
      </trans-unit>
      <trans-unit id="a2e8a639b5fd3e34ab4def1e7aac3383">
        <source>If you set this URL as the Main URL for the selected shop, all URLs set to this shop will be redirected to this URL (you can only have one Main URL per shop).</source>
        <target>If you set this URL as the Main URL for the selected shop, all URLs set to this shop will be redirected to this URL (you can only have one Main URL per shop).</target>
        <note>Line: 202</note>
      </trans-unit>
      <trans-unit id="819e18d335155ef4a9cdfe926ac8a3de">
        <source>Since the selected shop has no main URL, you have to set this URL as the Main URL.</source>
        <target>Since the selected shop has no main URL, you have to set this URL as the Main URL.</target>
        <note>Line: 204</note>
      </trans-unit>
      <trans-unit id="fe42e7cf02611b9719a6db6513de19eb">
        <source>The selected shop already has a Main URL. Therefore, if you set this one as the Main URL, the older Main URL will be set as a regular URL.</source>
        <target>The selected shop already has a Main URL. Therefore, if you set this one as the Main URL, the older Main URL will be set as a regular URL.</target>
        <note>Line: 208</note>
      </trans-unit>
      <trans-unit id="7e7b350e5cee624789b1e263ec59db26">
        <source>This is the physical folder for your store on the web server. Leave this field empty if your store is installed on the root path. For instance, if your store is available at www.example.com/my-store/, you must input my-store/ in this field.</source>
        <target>This is the physical folder for your store on the web server. Leave this field empty if your store is installed on the root path. For instance, if your store is available at www.example.com/my-store/, you must input my-store/ in this field.</target>
        <note>Line: 260</note>
      </trans-unit>
      <trans-unit id="29f3eb1bea3049b52600a18ad4d0b96c">
        <source>Warning: URL rewriting (e.g. mod_rewrite for Apache) seems to be disabled. If your Virtual URL doesn't work, please check with your hosting provider on how to activate URL rewriting.</source>
        <target>Warning: URL rewriting (e.g. mod_rewrite for Apache) seems to be disabled. If your Virtual URL doesn't work, please check with your hosting provider on how to activate URL rewriting.</target>
        <note>Line: 269</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Import/EntityField/Provider/CategoryFieldsProvider.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="11cd708df7411da4546d8580356e711a">
        <source>Ignore this field if you don't use the Multistore tool. If you leave this field empty, the default shop will be used.</source>
        <target>Ignore this field if you don't use the Multistore tool. If you leave this field empty, the default shop will be used.</target>
        <note>Line: 75</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Import/EntityField/Provider/CombinationFieldsProvider.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="5702db29cfdd93934ed330edb201ecf3">
        <source>Enable Advanced Stock Management on product (0 = No, 1 = Yes)</source>
        <target>Enable Advanced Stock Management on product (0 = No, 1 = Yes)</target>
        <note>Line: 90</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/Core/Import/EntityField/Provider/ProductFieldsProvider.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="7506b41cf1aa65b2663b038e465c0d6d">
        <source>Enable Advanced Stock Management on product (0 = No, 1 = Yes).</source>
        <target>Enable Advanced Stock Management on product (0 = No, 1 = Yes).</target>
        <note>Line: 155</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/AdvancedParameters/Performance/CombineCompressCacheType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="021007c7da195ab112330b846e0b1cbb">
        <source>This will add directives to your .htaccess file, which should improve caching and compression.</source>
        <target>This will add directives to your .htaccess file, which should improve caching and compression.</target>
        <note>Line: 52</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/AdvancedParameters/Performance/DebugModeType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="853f64470ff7bb429d4d1febf19a2d58">
        <source>Enable or disable debug mode.</source>
        <target>Enable or disable debug mode.</target>
        <note>Line: 52</note>
      </trans-unit>
      <trans-unit id="abef01780b7419768943a18d85666f09">
        <source>Enable or disable debug mode. Debug mode will enable extended error reporting, display the Symfony debug bar, and other features.</source>
        <target>Enable or disable debug mode. Debug mode will enable extended error reporting, display the Symfony debug bar, and other features.</target>
        <note>Line: 52</note>
      </trans-unit>
      <trans-unit id="913a55381fe0db5ad85c59b8a693fd18">
        <source>Enable or disable debug profiling. Debug profiling will display performance-related information under each page and help find performance bottlenecks in your store.</source>
        <target>Enable or disable debug profiling. Debug profiling will display performance-related information under each page and help find performance bottlenecks in your store.</target>
        <note>Line: 57</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/AdvancedParameters/Performance/MediaServersType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="7d7364eb90dcf245641e4139c6ab1a7c">
        <source>Name of the second domain of your shop, (e.g. myshop-media-server-1.com). If you do not have another domain, leave this field blank.</source>
        <target>Name of the second domain of your shop, (e.g. myshop-media-server-1.com). If you do not have another domain, leave this field blank.</target>
        <note>Line: 46</note>
      </trans-unit>
      <trans-unit id="80ca4dd5f99b6572c93cec540caff504">
        <source>Name of the third domain of your shop, (e.g. myshop-media-server-2.com). If you do not have another domain, leave this field blank.</source>
        <target>Name of the third domain of your shop, (e.g. myshop-media-server-2.com). If you do not have another domain, leave this field blank.</target>
        <note>Line: 52</note>
      </trans-unit>
      <trans-unit id="1e4e1915fcb0b296ce2ffb029b27d730">
        <source>Name of the fourth domain of your shop, (e.g. myshop-media-server-3.com). If you do not have another domain, leave this field blank.</source>
        <target>Name of the fourth domain of your shop, (e.g. myshop-media-server-3.com). If you do not have another domain, leave this field blank.</target>
        <note>Line: 58</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/AdvancedParameters/Performance/OptionalFeaturesType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="dadb7368382c41e65db131dde4acbddd">
        <source>Choose "No" to disable Product Combinations.</source>
        <target>Choose "No" to disable Product Combinations.</target>
        <note>Line: 67</note>
      </trans-unit>
      <trans-unit id="31d21c53f8b07a3adcac80c8fab067db">
        <source>You cannot set this parameter to No when combinations are already used by some of your products</source>
        <target>You cannot set this parameter to No when combinations are already used by some of your products</target>
        <note>Line: 68</note>
      </trans-unit>
      <trans-unit id="755b5f88357c721918923199f02a127d">
        <source>Choose "No" to disable Product Features.</source>
        <target>Choose "No" to disable Product Features.</target>
        <note>Line: 73</note>
      </trans-unit>
      <trans-unit id="07e451f21d83658a364e0548c86926b3">
        <source>Choose "No" to disable Customer Groups.</source>
        <target>Choose "No" to disable Customer Groups.</target>
        <note>Line: 77</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/AdvancedParameters/Performance/SmartyType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="30c802194b54f04e24a2abdc774c2f7e">
        <source>Should be enabled if you want to avoid to store the smarty cache on NFS.</source>
        <target>Should be enabled if you want to avoid to store the smarty cache on NFS.</target>
        <note>Line: 64</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Administration/GeneralType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="5b1438e7a734768ebe34a4f10535a4bc">
        <source>Choose a stability level for the modules downloaded from PrestaShop Addons Marketplace. All zips pushed on the marketplace are in stable state unless stated otherwise.</source>
        <target>Choose a stability level for the modules downloaded from PrestaShop Addons Marketplace. All zips pushed on the marketplace are in stable state unless stated otherwise.</target>
        <note>Line: 51</note>
      </trans-unit>
      <trans-unit id="0f81567617bb8ebc23f48e74d8ae8acf">
        <source>Check the IP address of the cookie in order to prevent your cookie from being stolen.</source>
        <target>Check the IP address of the cookie in order to prevent your cookie from being stolen.</target>
        <note>Line: 51</note>
      </trans-unit>
      <trans-unit id="20d6b6498eab9f749d55c9b53151e00a">
        <source>Set the amount of hours during which the front office cookies are valid. After that amount of time, the customer will have to log in again.</source>
        <target>Set the amount of hours during which the front office cookies are valid. After that amount of time, the customer will have to log in again.</target>
        <note>Line: 55</note>
      </trans-unit>
      <trans-unit id="b67a6169f1139b421eabe8278277b0ae">
        <source>When you access your back office and decide to stay logged in, your cookies lifetime defines your browser session. Set here the number of hours during which you want them valid before logging in again.</source>
        <target>When you access your back office and decide to stay logged in, your cookies lifetime defines your browser session. Set here the number of hours during which you want them valid before logging in again.</target>
        <note>Line: 60</note>
      </trans-unit>
      <trans-unit id="c8d06889e023d3130c1bf60009d5417b">
        <source>Allows you to declare if your cookie should be restricted to a first-party or same-site context.</source>
        <target>Allows you to declare if your cookie should be restricted to a first-party or same-site context.</target>
        <note>Line: 65</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Administration/UploadQuotaType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="eb191ef572b6d5c4e7a86734ba10fb5e">
        <source>Set the maximum size allowed for attachment files (in megabytes). This value has to be lower than or equal to the maximum file upload allotted by your server (currently: %size% MB).</source>
        <target>Set the maximum size allowed for attachment files (in megabytes). This value has to be lower than or equal to the maximum file upload allotted by your server (currently: %size% MB).</target>
        <note>Line: 71</note>
      </trans-unit>
      <trans-unit id="dae623d110235f2837ca0a6e753305b8">
        <source>Define the upload limit for a downloadable product (in megabytes). This value has to be lower or equal to the maximum file upload allotted by your server (currently: %size% MB).</source>
        <target>Define the upload limit for a downloadable product (in megabytes). This value has to be lower or equal to the maximum file upload allotted by your server (currently: %size% MB).</target>
        <note>Line: 103</note>
      </trans-unit>
      <trans-unit id="2a59c5a630e01a9a88e9bcb2863fd69f">
        <source>Define the upload limit for an image (in megabytes). This value has to be lower or equal to the maximum file upload allotted by your server (currently: %size% MB).</source>
        <target>Define the upload limit for an image (in megabytes). This value has to be lower or equal to the maximum file upload allotted by your server (currently: %size% MB).</target>
        <note>Line: 135</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Backup/BackupOptionsType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="1589ac76f2f88749f51028f09b23f9d4">
        <source>Drop existing tables during import.</source>
        <target>Drop existing tables during import.</target>
        <note>Line: 67</note>
      </trans-unit>
      <trans-unit id="a1e749fdff79ab0d40843bacb8fe0de2">
        <source>If enabled, the backup script will drop your tables prior to restoring data. (ie. "DROP TABLE IF EXISTS")</source>
        <target>If enabled, the backup script will drop your tables prior to restoring data. (ie. "DROP TABLE IF EXISTS")</target>
        <note>Line: 79</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Email/DkimConfigurationType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="10402f7c5b23f2156415aefabea831d2">
        <source>A DKIM selector usually looks like 12345.domain. It must match the name of your DNS record.</source>
        <target>A DKIM selector usually looks like 12345.domain. It must match the name of your DNS record.</target>
        <note>Line: 54</note>
      </trans-unit>
      <trans-unit id="7256aed5ef7ebd86c3b3d98480d9cc60">
        <source>The private key starts with -----BEGIN RSA PRIVATE KEY-----.</source>
        <target>The private key starts with -----BEGIN RSA PRIVATE KEY-----.</target>
        <note>Line: 60</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Email/EmailConfigurationType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="e6f52e23510fd010aa5460c96fd67142">
        <source>Where customers send messages from the order page.</source>
        <target>Where customers send messages from the order page.</target>
        <note>Line: 78</note>
      </trans-unit>
      <trans-unit id="3d24e5589702a2ecfd1d65813b4a892e">
        <source>Enable DKIM, fill in the fields and give it a try. If no email is sent, check logs.</source>
        <target>Enable DKIM, fill in the fields and give it a try. If no email is sent, check logs.</target>
        <note>Line: 113</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Email/SmtpConfigurationType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a60bc065c0296646c5a07e55c0cfff6e">
        <source>Fully qualified domain name (keep this field empty if you don't know).</source>
        <target>Fully qualified domain name (keep this field empty if you don't know).</target>
        <note>Line: 50</note>
      </trans-unit>
      <trans-unit id="2a3c280f73a3389c6ba4b2d87c8aa15f">
        <source>IP address or server name (e.g. smtp.mydomain.com).</source>
        <target>IP address or server name (e.g. smtp.mydomain.com).</target>
        <note>Line: 55</note>
      </trans-unit>
      <trans-unit id="a5d35e0a66d41c7d12c0df6e643fa0ff">
        <source>Leave blank if not applicable.</source>
        <target>Leave blank if not applicable.</target>
        <note>Line: 67</note>
      </trans-unit>
      <trans-unit id="5b8df4265c79cd43831e740281850c84">
        <source>SSL does not seem to be available on your server.</source>
        <target>SSL does not seem to be available on your server.</target>
        <note>Line: 81</note>
      </trans-unit>
      <trans-unit id="762160912d2bac8eb64bc00b711d6f6c">
        <source>Port number to use.</source>
        <target>Port number to use.</target>
        <note>Line: 86</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Employee/EmployeeType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a951bd27ed3722e63e13ff4c73a3749a">
        <source>Allow or deny this employee's access to the Admin panel.</source>
        <target>Allow or deny this employee's access to the Admin panel.</target>
        <note>Line: 203</note>
      </trans-unit>
      <trans-unit id="7774f7ea613f49f5246f9ff5e96d29de">
        <source>Select the stores the employee is allowed to access.</source>
        <target>Select the stores the employee is allowed to access.</target>
        <note>Line: 220</note>
      </trans-unit>
      <trans-unit id="c4e326fcaa83c6b1ca3856175bb86d2e">
        <source>This page will be displayed just after login.</source>
        <target>This page will be displayed just after login.</target>
        <note>Line: 229</note>
      </trans-unit>
      <trans-unit id="d222c4d0463bd3f146536ac89c0d7c77">
        <source>Password should be at least %num% characters long.</source>
        <target>Password should be at least %num% characters long.</target>
        <note>Line: 173</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Import/ImportType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="45af6698125d6f9be9517cd8879d0285">
        <source>e.g. 1; Blouse; 129.90; 5</source>
        <target>e.g. 1; Blouse; 129.90; 5</target>
        <note>Line: 83</note>
      </trans-unit>
      <trans-unit id="7d97fc4385e615ed69cc4e1613790123">
        <source>e.g. Blouse; red.jpg, blue.jpg, green.jpg; 129.90 </source>
        <target>e.g. Blouse; red.jpg, blue.jpg, green.jpg; 129.90</target>
        <note>Line: 87</note>
      </trans-unit>
      <trans-unit id="1d3be023802846145f9b75fc90b4cf21">
        <source>If enabled, the product's reference number MUST be unique!</source>
        <target>If enabled, the product's reference number MUST be unique!</target>
        <note>Line: 107</note>
      </trans-unit>
      <trans-unit id="193ec293f59bd6bafda920d87cb07212">
        <source>Enable this option to keep your imported items’ ID number as is. Otherwise, PrestaShop will ignore them and create auto-incremented ID numbers.</source>
        <target>Enable this option to keep your imported items’ ID number as is. Otherwise, PrestaShop will ignore them and create auto-incremented ID numbers.</target>
        <note>Line: 120</note>
      </trans-unit>
      <trans-unit id="4719ac638ec9a37d72c81b550b911945">
        <source>Receive an email when the import is complete. It can be useful when handling large files, as the import may take some time.</source>
        <target>Receive an email when the import is complete. It can be useful when handling large files, as the import may take some time.</target>
        <note>Line: 124</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Logs/LogsByEmailType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="7dda1e48a0dcceda4b3e9ce35271cc2c">
        <source>Click on "None" to disable log alerts by email or enter the recipients of these emails in the following field.</source>
        <target>Click on "None" to disable log alerts by email or enter the recipients of these emails in the following field.</target>
        <note>Line: 56</note>
      </trans-unit>
      <trans-unit id="f88eb52dea2a0d6eb6d90304a91be4a0">
        <source>Log alerts will be sent to these emails. Please use a comma to separate them (e.g. <EMAIL>, <EMAIL>).</source>
        <target>Log alerts will be sent to these emails. Please use a comma to separate them (e.g. <EMAIL>, <EMAIL>).</target>
        <note>Line: 66</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/RequestSql/SqlRequestSettingsType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="408b9afa3037a223d76ff924a1e98fad">
        <source>Enabling multi-statements queries increases the risk of SQL injection vulnerabilities being exploited.</source>
        <target>Enabling multi-statements queries increases the risk of SQL injection vulnerabilities being exploited.</target>
        <note>Line: 54</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Security/GeneralType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="4b620493cd5571cb4af81dba9c68812e">
        <source>Back office pages require the use of a token. This protection can be disabled if needed.</source>
        <target>Back office pages require the use of a token. This protection can be disabled if needed.</target>
        <note>Line: 49</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Webservice/WebserviceConfigurationType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="e8602bda90ef985a71054950e6f1981e">
        <source>Before activating the webservice, you must be sure to: </source>
        <target>Before activating the webservice, you must be sure to:</target>
        <note>Line: 46</note>
      </trans-unit>
      <trans-unit id="45d7b1d28cb1afbb45fdb620db725c10">
        <source>Check that URL rewriting is available on this server.</source>
        <target>Check that URL rewriting is available on this server.</target>
        <note>Line: 51</note>
      </trans-unit>
      <trans-unit id="000552d95fb9e18ac08ca27098bf1d41">
        <source>Check that the six methods GET, POST, PUT, PATCH, DELETE and HEAD are supported by this server.</source>
        <target>Check that the six methods GET, POST, PUT, PATCH, DELETE and HEAD are supported by this server.</target>
        <note>Line: 56</note>
      </trans-unit>
      <trans-unit id="c03f07bc287e77654a26af409b4d93fa">
        <source>Before choosing "Yes", check that PHP is not configured as an Apache module on your server.</source>
        <target>Before choosing "Yes", check that PHP is not configured as an Apache module on your server.</target>
        <note>Line: 76</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Configure/AdvancedParameters/Webservice/WebserviceKeyType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a5cb039db72d4c240b6cca34b48d61bc">
        <source>Quick description of the key: who it is for, what permissions it has, etc.</source>
        <target>Quick description of the key: who it is for, what permissions it has, etc.</target>
        <note>Line: 133</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Backup/Blocks/backup_info.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="20fbc8f64351875f498d7d6f8c91810b">
        <source>How to restore a database backup in 10 easy steps</source>
        <target>How to restore a database backup in 10 easy steps</target>
        <note>Line: 28</note>
      </trans-unit>
      <trans-unit id="7bf215c957e661c3bd8ad7f6221675c9">
        <source>Set "Enable Shop" to "No" in the "Maintenance" page under the "Preferences" menu.</source>
        <target>Set "Enable Shop" to "No" in the "Maintenance" page under the "Preferences" menu.</target>
        <note>Line: 30</note>
      </trans-unit>
      <trans-unit id="2dc09c116021fe75dd928d368a35e9d7">
        <source>Download the backup from the list below or from your FTP server (in the folder "admin/backups").</source>
        <target>Download the backup from the list below or from your FTP server (in the folder "admin/backups").</target>
        <note>Line: 31</note>
      </trans-unit>
      <trans-unit id="3a86c87939c6bbaba4cb2e44e4734146">
        <source>Check the backup integrity: Look for errors, incomplete file, etc... Be sure to verify all of your data.</source>
        <target>Check the backup integrity: Look for errors, incomplete file, etc... Be sure to verify all of your data.</target>
        <note>Line: 32</note>
      </trans-unit>
      <trans-unit id="0ba394816eab81959ea7c442801b0819">
        <source>Please ask your hosting provider for "phpMyAdmin" access to your database.</source>
        <target>Please ask your hosting provider for "phpMyAdmin" access to your database.</target>
        <note>Line: 33</note>
      </trans-unit>
      <trans-unit id="04abc58df1575dd465a28c86d859d302">
        <source>Connect to "phpMyAdmin" and select your current database.</source>
        <target>Connect to "phpMyAdmin" and select your current database.</target>
        <note>Line: 34</note>
      </trans-unit>
      <trans-unit id="197ebd1d022def92dd1c64aae3320d6a">
        <source>Unless you enabled the "Drop existing tables" option, you must delete all tables from your current database.</source>
        <target>Unless you enabled the "Drop existing tables" option, you must delete all tables from your current database.</target>
        <note>Line: 35</note>
      </trans-unit>
      <trans-unit id="7d6044f9c5ec3bb7b89ac9328a871b2f">
        <source>At the top of the screen, please select the "Import" tab</source>
        <target>At the top of the screen, please select the "Import" tab</target>
        <note>Line: 36</note>
      </trans-unit>
      <trans-unit id="2e516ba34e888923fa0f76e5dad9e24a">
        <source>Click on the "%phpmyadmin_browse_label%" button and select the backup file from your hard drive.</source>
        <target>Click on the "%phpmyadmin_browse_label%" button and select the backup file from your hard drive.</target>
        <note>Line: 37</note>
      </trans-unit>
      <trans-unit id="85614411a0c7c3eb8c7c7a81be9fb918">
        <source>Check the maximum filesize allowed (e.g. Max: 16MB)</source>
        <target>Check the maximum filesize allowed (e.g. Max: 16MB)</target>
        <note>Line: 39</note>
      </trans-unit>
      <trans-unit id="d24e59c4885823395588f1b480876da5">
        <source>If your backup file exceeds this limit, contact your hosting provider for assistance. </source>
        <target>If your backup file exceeds this limit, contact your hosting provider for assistance.</target>
        <note>Line: 40</note>
      </trans-unit>
      <trans-unit id="4a67f50a5bcc2387568637556809af28">
        <source>Click on the "%phpmyadmin_go_label%" button and please wait patiently for the import process to conclude. This may take several minutes.</source>
        <target>Click on the "%phpmyadmin_go_label%" button and please wait patiently for the import process to conclude. This may take several minutes.</target>
        <note>Line: 42</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Blocks/import_panel.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="084ad8abffc138f8d43f0a77824396b9">
        <source>You can read information on import at:</source>
        <target>You can read information on import at:</target>
        <note>Line: 39</note>
      </trans-unit>
      <trans-unit id="8a8e09c10c464ed914e3e0278ea53164">
        <source>https://docs.prestashop-project.org/1.7-documentation/user-guide/configuring-shop/advanced-parameters/import</source>
        <target>https://docs.prestashop-project.org/1.7-documentation/user-guide/configuring-shop/advanced-parameters/import</target>
        <note>Line: 41</note>
      </trans-unit>
      <trans-unit id="3bb4a695db0c87a3d70a2deffddeb743">
        <source>Read more about the CSV format at:</source>
        <target>Read more about the CSV format at:</target>
        <note>Line: 45</note>
      </trans-unit>
      <trans-unit id="9d23c31f2dcc1ace9296037f327563a4">
        <source>https://en.wikipedia.org/wiki/Comma-separated_values</source>
        <target>https://en.wikipedia.org/wiki/Comma-separated_values</target>
        <note>Line: 47</note>
      </trans-unit>
      <trans-unit id="a27780d9bb7b3595998465aadcaaeed7">
        <source>Allowed formats: .csv, .xls, .xlsx, .xlst, .ods, .ots</source>
        <target>Allowed formats: .csv, .xls, .xlsx, .xlst, .ods, .ots</target>
        <note>Line: 90</note>
      </trans-unit>
      <trans-unit id="c9f7248e8f5712f5438def77389e1c46">
        <source>Only UTF-8 and ISO 8859-1 encodings are allowed</source>
        <target>Only UTF-8 and ISO 8859-1 encodings are allowed</target>
        <note>Line: 91</note>
      </trans-unit>
      <trans-unit id="6578c8c9a3dbadb0a3672c3ca6c0b513">
        <source>You can also upload your file via FTP to the following directory: %s .</source>
        <target>You can also upload your file via FTP to the following directory: %s .</target>
        <note>Line: 92</note>
      </trans-unit>
      <trans-unit id="69b2fa39bac2b03f7b5c8e63bf5c6e99">
        <source>https://docs.prestashop-project.org/v.8-documentation/user-guide/configuring-shop/advanced-parameters/import</source>
        <target>https://docs.prestashop-project.org/v.8-documentation/user-guide/configuring-shop/advanced-parameters/import</target>
        <note>Line: 41</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Email/Blocks/email_configuration.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="71032b7b42c511e76dd7054aba7c1fb0">
        <source>Fill in the fields below to set up DKIM signing of outgoing emails. This will reduce the likelihood of your emails being marked as spam.
              You can get the data below from your server administrator or generate it yourself by using one of the freely available tools, such as dkimcore.org.
              Also, make sure that your server is registered as an authorized sender in your SPF record. </source>
        <target>Fill in the fields below to set up DKIM signing of outgoing emails. This will reduce the likelihood of your emails being marked as spam.
              You can get the data below from your server administrator or generate it yourself by using one of the freely available tools, such as dkimcore.org.
              Also, make sure that your server is registered as an authorized sender in your SPF record.</target>
        <note>Line: 46</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Employee/Blocks/form.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="d970370797ba19bba5524b480d3526fb">
        <source>Allow or disallow this employee to log in to the Admin panel.</source>
        <target>Allow or disallow this employee to log in to the Admin panel.</target>
        <note>Line: 133</note>
      </trans-unit>
      <trans-unit id="1a7f0b984884e66e95a86b9b5d857b5b">
        <source>Select the shops the employee is allowed to access.</source>
        <target>Select the shops the employee is allowed to access.</target>
        <note>Line: 148</note>
      </trans-unit>
      <trans-unit id="5b42912d040310c64b30c30852ae4683">
        <source>This page will be displayed right after signing in.</source>
        <target>This page will be displayed right after signing in.</target>
        <note>Line: 155</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/LogsPage/Blocks/severity_levels.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="b80c52e7f679fba3c2837b42705fe779">
        <source>Informative only</source>
        <target>Informative only</target>
        <note>Line: 36</note>
      </trans-unit>
      <trans-unit id="0eaadb4fcb48a0a0ed7bc9868be9fbaa">
        <source>Warning</source>
        <target>Warning</target>
        <note>Line: 41</note>
      </trans-unit>
      <trans-unit id="902b0d55fddef6f8d651fe1035b7d4bd">
        <source>Error</source>
        <target>Error</target>
        <note>Line: 46</note>
      </trans-unit>
      <trans-unit id="4a378407770df6a42474ec5db9f54740">
        <source>Major issue (crash)!</source>
        <target>Major issue (crash)!</target>
        <note>Line: 51</note>
      </trans-unit>
      <trans-unit id="3979bb684920e9e9dc86e266f93d8c87">
        <source>Severity levels</source>
        <target>Severity levels</target>
        <note>Line: 29</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/RequestSql/index.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="68db67219aa1fd6e5f8820e20fd78b25">
        <source>How do I create a new SQL query?</source>
        <target>How do I create a new SQL query?</target>
        <note>Line: 32</note>
      </trans-unit>
      <trans-unit id="bd98e6133da373d5d4d0241cae2e5df5">
        <source>Click "%add_new_label%".</source>
        <target>Click "%add_new_label%".</target>
        <note>Line: 34</note>
      </trans-unit>
      <trans-unit id="6fe4a9d3d016ffb7ac0c79b9b34f759b">
        <source>Fill in the fields and click "%save_label%".</source>
        <target>Fill in the fields and click "%save_label%".</target>
        <note>Line: 35</note>
      </trans-unit>
      <trans-unit id="b6244b10c31060f4136acddff13a2729">
        <source>You can then view the query results by clicking on the "%view_label%" action in the dropdown menu</source>
        <target>You can then view the query results by clicking on the "%view_label%" action in the dropdown menu</target>
        <note>Line: 36</note>
      </trans-unit>
      <trans-unit id="09fac4ee316f450e3defd79540c2d9fe">
        <source>You can also export the query results as a CSV file by clicking on the "%export_label%" button</source>
        <target>You can also export the query results as a CSV file by clicking on the "%export_label%" button</target>
        <note>Line: 37
Comment: Recommended modules will be attached to here.</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Security/clear_form.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="54cde7677b5a5624041c00c100224cbc">
        <source>All outdated sessions will be automatically cleared after the first connection attempt, but you can do it manually now if needed.</source>
        <target>All outdated sessions will be automatically cleared after the first connection attempt, but you can do it manually now if needed.</target>
        <note>Line: 39</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/Security/index.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="04da1735a100af1089236ff39c5021ca">
        <source>Scores are integers from 0 to 4.</source>
        <target>Scores are integers from 0 to 4.</target>
        <note>Line: 67</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/administration.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="2566fed9ede40ae26636b773594094cf">
        <source>Notifications are numbered bubbles displayed at the very top of your back office, right next to the shop's name. They display the number of new items since you last clicked on them.</source>
        <target>Notifications are numbered bubbles displayed at the very top of your back office, right next to the shop's name. They display the number of new items since you last clicked on them.</target>
        <note>Line: 86</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Configure/AdvancedParameters/performance.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="767bd2cbfe41f0aa3f3e4ff306a75b89">
        <source>This feature allows you to identify modules that might be causing bugs on your store. Disable all non-built-in modules (not listed in composer.json). Then, re-enable each module one by one and check that everything works properly before moving on to the next one.</source>
        <target>This feature allows you to identify modules that might be causing bugs on your store. Disable all non-built-in modules (not listed in composer.json). Then, re-enable each module one by one and check that everything works properly before moving on to the next one.</target>
        <note>Line: 93</note>
      </trans-unit>
      <trans-unit id="bb3656a5447418f8f2644f3ac91b5c6f">
        <source>Some features can be disabled in order to improve performance.</source>
        <target>Some features can be disabled in order to improve performance.</target>
        <note>Line: 137</note>
      </trans-unit>
      <trans-unit id="460e080fb0af5e1ca93a24dbf1e3da2f">
        <source>CCC allows you to reduce the loading time of your page. With these settings you will gain performance without even touching the code of your theme. Make sure, however, that your theme is compatible with PrestaShop 1.7+. Otherwise, CCC will cause problems.</source>
        <target>CCC allows you to reduce the loading time of your page. With these settings you will gain performance without even touching the code of your theme. Make sure, however, that your theme is compatible with PrestaShop 1.7+. Otherwise, CCC will cause problems.</target>
        <note>Line: 169</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/TwigTemplateForm/prestashop_ui_kit_base.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="4bbb8f967da6d1a610596d7257179c2b">
        <source>Invalid</source>
        <target>Invalid</target>
        <note>Line: 1604</note>
      </trans-unit>
      <trans-unit id="26b63f278101527e06a5547719568bb5">
        <source>Okay</source>
        <target>Okay</target>
        <note>Line: 1605</note>
      </trans-unit>
      <trans-unit id="0c6ad70beb3a7e76c3fc7adab7c46acc">
        <source>Good</source>
        <target>Good</target>
        <note>Line: 1606</note>
      </trans-unit>
      <trans-unit id="b8dc7c80939667637db7ac31ece215b9">
        <source>Fabulous</source>
        <target>Fabulous</target>
        <note>Line: 1607</note>
      </trans-unit>
    </body>
  </file>
</xliff>
