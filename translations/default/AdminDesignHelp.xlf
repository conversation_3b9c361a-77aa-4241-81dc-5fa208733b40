<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file original="admin-dev/themes/default/template/controllers/images/content.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="5fe1c6d39261cafbe5e6d827330d3841">
        <source>By default, all images settings are already installed in your store. Do not delete them, you will need it!</source>
        <target>By default, all images settings are already installed in your store. Do not delete them, you will need it!</target>
        <note>Line: 27</note>
      </trans-unit>
      <trans-unit id="85cf8b897040b0bc24d97db52d0922ee">
        <source>Select "No" only if your server timed out and you need to resume the regeneration.</source>
        <target>Select "No" only if your server timed out and you need to resume the regeneration.</target>
        <note>Line: 121</note>
      </trans-unit>
    </body>
  </file>
  <file original="admin-dev/themes/default/template/controllers/modules_positions/form.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="0a4dbce7e33277b97c5f5667715e0b46">
        <source>Please select a module</source>
        <target>Please select a module</target>
        <note>Line: 42</note>
      </trans-unit>
      <trans-unit id="0b08e9b451b556f1bdd62b285ee2a9df">
        <source>Select a module above before choosing from available hooks</source>
        <target>Select a module above before choosing from available hooks</target>
        <note>Line: 55</note>
      </trans-unit>
      <trans-unit id="74cd700f9b419dd789dc7d8d1cbf49ef">
        <source>Please specify the files for which you do not want the module to be displayed.</source>
        <target>Please specify the files for which you do not want the module to be displayed.</target>
        <note>Line: 81</note>
      </trans-unit>
      <trans-unit id="07b7dbb810d745fc3891d68448f3f213">
        <source>Please input each filename, separated by a comma (",").</source>
        <target>Please input each filename, separated by a comma (",").</target>
        <note>Line: 82</note>
      </trans-unit>
      <trans-unit id="804a997021a16e79a9e6ef9a2bf39e3d">
        <source>You can also click the filename in the list below, and even make a multiple selection by keeping the Ctrl key pressed while clicking, or choose a whole range of filename by keeping the Shift key pressed while clicking.</source>
        <target>You can also click the filename in the list below, and even make a multiple selection by keeping the Ctrl key pressed while clicking, or choose a whole range of filename by keeping the Shift key pressed while clicking.</target>
        <note>Line: 83</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminImagesController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="48658689e18e12e430668e1f2419b0d0">
        <source>JPEG images have a small file size and standard quality. PNG images have a larger file size, a higher quality and support transparency. Note that in all cases the image files will have the .jpg extension.</source>
        <target>JPEG images have a small file size and standard quality. PNG images have a larger file size, a higher quality and support transparency. Note that in all cases the image files will have the .jpg extension.</target>
        <note>Line: 285</note>
      </trans-unit>
      <trans-unit id="c031c7c1498d22082b4dcbac4fac27d4">
        <source>WARNING: This feature may not be compatible with your theme, or with some of your modules. In particular, PNG mode is not compatible with the Watermark module. If you encounter any issues, turn it off by selecting "Use JPEG".</source>
        <target>WARNING: This feature may not be compatible with your theme, or with some of your modules. In particular, PNG mode is not compatible with the Watermark module. If you encounter any issues, turn it off by selecting "Use JPEG".</target>
        <note>Line: 286</note>
      </trans-unit>
      <trans-unit id="576d1a59cae85fc41c31d27433e17d37">
        <source>Ranges from 0 (worst quality, smallest file) to 100 (best quality, biggest file).</source>
        <target>Ranges from 0 (worst quality, smallest file) to 100 (best quality, biggest file).</target>
        <note>Line: 201</note>
      </trans-unit>
      <trans-unit id="49caff79e5dca417b2b2c37fc7ef61af">
        <source>Recommended: 90.</source>
        <target>Recommended: 90.</target>
        <note>Line: 184</note>
      </trans-unit>
      <trans-unit id="c1a1204bac358b5068446c93ec34bc87">
        <source>PNG compression is lossless: unlike JPG, you do not lose image quality with a high compression ratio. However, photographs will compress very badly.</source>
        <target>PNG compression is lossless: unlike JPG, you do not lose image quality with a high compression ratio. However, photographs will compress very badly.</target>
        <note>Line: 192</note>
      </trans-unit>
      <trans-unit id="ce1cd64739c592c80f58012b38f5c42f">
        <source>Ranges from 0 (biggest file) to 9 (smallest file, slowest decompression).</source>
        <target>Ranges from 0 (biggest file) to 9 (smallest file, slowest decompression).</target>
        <note>Line: 192</note>
      </trans-unit>
      <trans-unit id="4b1fa40679bbc322bc244f9f09830681">
        <source>Recommended: 7.</source>
        <target>Recommended: 7.</target>
        <note>Line: 192</note>
      </trans-unit>
      <trans-unit id="d9fe6fa30a999a9a89d81de9faf02df8">
        <source>Recommended: %d.</source>
        <target>Recommended: %d.</target>
        <note>Line: 206</note>
      </trans-unit>
      <trans-unit id="e425d33122fa9152a34d746c60b0903b">
        <source>The maximum file size of pictures that customers can upload to customize a product (in bytes).</source>
        <target>The maximum file size of pictures that customers can upload to customize a product (in bytes).</target>
        <note>Line: 237</note>
      </trans-unit>
      <trans-unit id="273ad45c9ec7fbd15b705f1d8b272bdb">
        <source>Width of product customization pictures that customers can upload (in pixels).</source>
        <target>Width of product customization pictures that customers can upload (in pixels).</target>
        <note>Line: 247</note>
      </trans-unit>
      <trans-unit id="fd70ea0d8dbbe0a675d717812b3253fe">
        <source>Height of product customization pictures that customers can upload (in pixels).</source>
        <target>Height of product customization pictures that customers can upload (in pixels).</target>
        <note>Line: 258</note>
      </trans-unit>
      <trans-unit id="d20e2b80b07e17b035a7c8fe09deca44">
        <source>This will generate an additional file for each image (thus doubling your total amount of images). Resolution of these images will be twice higher.</source>
        <target>This will generate an additional file for each image (thus doubling your total amount of images). Resolution of these images will be twice higher.</target>
        <note>Line: 272</note>
      </trans-unit>
      <trans-unit id="d869df123081c24a3d5f5145cb5368ea">
        <source>Enable to optimize the display of your images on high pixel density screens.</source>
        <target>Enable to optimize the display of your images on high pixel density screens.</target>
        <note>Line: 273</note>
      </trans-unit>
      <trans-unit id="bc10c6548185bd449a823faf9c171596">
        <source>This should be set to yes unless you successfully moved images in "Images" page under the "Preferences" menu.</source>
        <target>This should be set to yes unless you successfully moved images in "Images" page under the "Preferences" menu.</target>
        <note>Line: 295</note>
      </trans-unit>
      <trans-unit id="0e10ed6e73d3301bee138a59a23c5de9">
        <source>Letters, underscores and hyphens only (e.g. "small_custom", "cart_medium", "large", "thickbox_extra-large").</source>
        <target>Letters, underscores and hyphens only (e.g. "small_custom", "cart_medium", "large", "thickbox_extra-large").</target>
        <note>Line: 315</note>
      </trans-unit>
      <trans-unit id="845d4d07d4cfe4d1ccadcbab72efe199">
        <source>Maximum image width in pixels.</source>
        <target>Maximum image width in pixels.</target>
        <note>Line: 324</note>
      </trans-unit>
      <trans-unit id="0afd1617fde01be0d13f62668e7ad4f1">
        <source>Maximum image height in pixels.</source>
        <target>Maximum image height in pixels.</target>
        <note>Line: 333</note>
      </trans-unit>
      <trans-unit id="a61f835bd3188471923b2da2717091ec">
        <source>This type will be used for Product images.</source>
        <target>This type will be used for Product images.</target>
        <note>Line: 341</note>
      </trans-unit>
      <trans-unit id="1ad8a6b5f7d9fc6ca597ad11103fab6e">
        <source>This type will be used for Category images.</source>
        <target>This type will be used for Category images.</target>
        <note>Line: 362</note>
      </trans-unit>
      <trans-unit id="f193abb289c02779dabbdae3beb6e9e1">
        <source>This type will be used for Brand images.</source>
        <target>This type will be used for Brand images.</target>
        <note>Line: 382</note>
      </trans-unit>
      <trans-unit id="01e1e70a11e81d8327a7a565869fb268">
        <source>This type will be used for Supplier images.</source>
        <target>This type will be used for Supplier images.</target>
        <note>Line: 402</note>
      </trans-unit>
      <trans-unit id="0096ae72d90ea092533743553617e0a5">
        <source>This type will be used for Store images.</source>
        <target>This type will be used for Store images.</target>
        <note>Line: 422</note>
      </trans-unit>
      <trans-unit id="8f12dd450ceed5b4da6a4d6d11b1bd12">
        <source>Choose which image formats you want to be generated. Base image will always have .jpg extension, other formats will have .webp or .avif.</source>
        <target>Choose which image formats you want to be generated. Base image will always have .jpg extension, other formats will have .webp or .avif.</target>
        <note>Line: 138</note>
      </trans-unit>
    </body>
  </file>
  <file original="controllers/admin/AdminModulesPositionsController.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a06929291c19a7718975cb8c24dea961">
        <source>E.g. address, addresses, attachment</source>
        <target>E.g. address, addresses, attachment</target>
        <note>Line: 362</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Improve/Design/Pages/CmsPageType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="2acc315907b6b57c444b1885666f5bdd">
        <source>Used in the h1 page tag, and as the default title tag value.</source>
        <target>Used in the h1 page tag, and as the default title tag value.</target>
        <note>Line: 106</note>
      </trans-unit>
      <trans-unit id="27043bc0eeca71692edf6c51fef16ee0">
        <source>Used to override the title tag value. If left blank, the default title value is used.</source>
        <target>Used to override the title tag value. If left blank, the default title value is used.</target>
        <note>Line: 154</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Form/Admin/Improve/Design/Theme/ImportThemeType.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="0d03fc8cc7c22c4ff80c5cda6f56ce8e">
        <source>Browse your computer files and select the Zip file for your new theme.</source>
        <target>Browse your computer files and select the Zip file for your new theme.</target>
        <note>Line: 71</note>
      </trans-unit>
      <trans-unit id="f545e8220cd6f37617c495e59695f7f3">
        <source>Indicate the complete URL to an online Zip file that contains your new theme. For instance, "http://example.com/files/theme.zip".</source>
        <target>Indicate the complete URL to an online Zip file that contains your new theme. For instance, "http://example.com/files/theme.zip".</target>
        <note>Line: 83</note>
      </trans-unit>
      <trans-unit id="87c309a4d1deb851c8522127c1aeae54">
        <source>This selector lists the Zip files that you uploaded in the '/themes' folder.</source>
        <target>This selector lists the Zip files that you uploaded in the '/themes' folder.</target>
        <note>Line: 91</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Improve/Design/Cms/Blocks/showcase_card.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="80ccab2796a85566dac9af41ab0bf89b">
        <source>Create meaningful content</source>
        <target>Create meaningful content</target>
        <note>Line: 33</note>
      </trans-unit>
      <trans-unit id="4cd30cbec90b9e4a0b25f4e3222e417b">
        <source>Because it is not just selling products but also creating a universe, build pages to tell stories and catch your visitors’ interest, to turn them into loyal customers.</source>
        <target>Because it is not just selling products but also creating a universe, build pages to tell stories and catch your visitors’ interest, to turn them into loyal customers.</target>
        <note>Line: 34</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Improve/Design/Cms/index.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="cabfe7563d12c3e047000dc5703d9709">
        <source>Add new page category</source>
        <target>Add new page category</target>
        <note>Line: 31</note>
      </trans-unit>
      <trans-unit id="aa6f77f824eff8e896510f1bbaf9f8e5">
        <source>Add new page</source>
        <target>Add new page</target>
        <note>Line: 36</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Improve/Design/MailTheme/Blocks/configuration_form.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="1dda8b854d639e61bee497c7d22f8057">
        <source>This won't regenerate email templates, it only sets the default email theme for future generation (when a language is installed for example).</source>
        <target>This won't regenerate email templates, it only sets the default email theme for future generation (when a language is installed for example).</target>
        <note>Line: 40</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Improve/Design/MailTheme/Blocks/generate_mails_form.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a286fbcf840903be7c9251c14ce9ac03">
        <source>PrestaShop's email templates are stored in the "mails" folder, but they can be overridden by your current theme's own "mails" folder. Using this option enables to overwrite emails from your current theme.</source>
        <target>PrestaShop's email templates are stored in the "mails" folder, but they can be overridden by your current theme's own "mails" folder. Using this option enables to overwrite emails from your current theme.</target>
        <note>Line: 62</note>
      </trans-unit>
      <trans-unit id="d8533de1b9f083f6a42b644fea80a6a0">
        <source>No emails were detected in any theme folder so this field is disabled.</source>
        <target>No emails were detected in any theme folder so this field is disabled.</target>
        <note>Line: 68</note>
      </trans-unit>
      <trans-unit id="0705d2ffde737b9365c1f84715f6f66f">
        <source>By default, existing email template files are not modified to avoid deleting any modification you may have done. Enable this option to force the overwrite.</source>
        <target>By default, existing email template files are not modified to avoid deleting any modification you may have done. Enable this option to force the overwrite.</target>
        <note>Line: 76</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Improve/Design/Theme/Blocks/logo_configuration.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="04f1f93771bf12b0b80ae6a86ceb5cae">
        <source>Will appear on your main page. Recommended size for the default theme: height %height% and width %width%.</source>
        <target>Will appear on your main page. Recommended size for the default theme: height %height% and width %width%.</target>
        <note>Line: 34</note>
      </trans-unit>
      <trans-unit id="aeec498c2bb6b53e8eb24752af092828">
        <source>Will appear on email headers. If undefined, the header logo will be used.</source>
        <target>Will appear on email headers. If undefined, the header logo will be used.</target>
        <note>Line: 88</note>
      </trans-unit>
      <trans-unit id="b0297d22aade28eda5efbeb5c2e21e97">
        <source>Will appear on invoice headers.</source>
        <target>Will appear on invoice headers.</target>
        <note>Line: 105</note>
      </trans-unit>
      <trans-unit id="544cd7788ebd8916e7191e7a8fd0db57">
        <source>Warning: if no invoice logo is available, the main logo will be used instead.</source>
        <target>Warning: if no invoice logo is available, the main logo will be used instead.</target>
        <note>Line: 117</note>
      </trans-unit>
      <trans-unit id="e91d6b1f1f3836b0126eb26760a0eb49">
        <source>It is the small icon that appears in browser tabs, next to the title.</source>
        <target>It is the small icon that appears in browser tabs, next to the title.</target>
        <note>Line: 131</note>
      </trans-unit>
      <trans-unit id="3fb044d8f9136df5b2f0087f8a8fb0c9">
        <source>Use our [1]favicon generator on PrestaShop Marketplace[/1] to boost your brand image!</source>
        <target>Use our [1]favicon generator on PrestaShop Marketplace[/1] to boost your brand image!</target>
        <note>Line: 147</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Improve/Design/Theme/Blocks/rtl_configuration.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="46b1aed6e580d075f9d45d67f8a65164">
        <source>Be careful! Please check your theme in an RTL language before generating the RTL stylesheet: your theme could be already adapted to RTL.
Once you enable the "%generate_rtl_label%" option, any RTL-specific file that you might have added to your theme might be deleted by the created stylesheet.</source>
        <target>Be careful! Please check your theme in an RTL language before generating the RTL stylesheet: your theme could be already adapted to RTL.
Once you enable the "%generate_rtl_label%" option, any RTL-specific file that you might have added to your theme might be deleted by the created stylesheet.</target>
        <note>Line: 37</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/Improve/Design/Theme/index.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="6894e313abf58dc29e27e6aef50b2f03">
        <source>You must select a shop from the above list if you wish to choose a theme.</source>
        <target>You must select a shop from the above list if you wish to choose a theme.</target>
        <note>Line: 49</note>
      </trans-unit>
    </body>
  </file>
  <file original="src/PrestaShopBundle/Resources/views/Admin/macros.html.twig" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="a1db50531c5baec8c2b17b2c58790c09">
        <source>You are editing this page for a specific shop or group. Click "%yes_label%" to check all fields, "%no_label%" to uncheck all.</source>
        <target>You are editing this page for a specific shop or group. Click "%yes_label%" to check all fields, "%no_label%" to uncheck all.</target>
        <note>Line: 294</note>
      </trans-unit>
      <trans-unit id="d97ccb3e617d930d52ffd95e5db2c101">
        <source>If you check a field, change its value, and save, the multistore behavior will not apply to this shop (or group), for this particular parameter.</source>
        <target>If you check a field, change its value, and save, the multistore behavior will not apply to this shop (or group), for this particular parameter.</target>
        <note>Line: 295</note>
      </trans-unit>
    </body>
  </file>
</xliff>
