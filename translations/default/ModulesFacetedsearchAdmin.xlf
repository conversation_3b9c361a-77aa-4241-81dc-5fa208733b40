<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file original="modules/ps_facetedsearch/ps_facetedsearch.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="d67b7992a94709663ce72ce9c1606597">
        <source>Faceted search</source>
        <target>Faceted search</target>
        <note>Line: 107</note>
      </trans-unit>
      <trans-unit id="2b998534020532daa37c8948b41f3116">
        <source>Filter your catalog to help visitors picture the category tree and browse your store easily.</source>
        <target>Filter your catalog to help visitors picture the category tree and browse your store easily.</target>
        <note>Line: 108</note>
      </trans-unit>
      <trans-unit id="ccc12c5568381293a27db0232877937b">
        <source>Filter template name required (cannot be empty)</source>
        <target>Filter template name required (cannot be empty)</target>
        <note>Line: 606</note>
      </trans-unit>
      <trans-unit id="8c97e587c1b4e519bec26f3903561da3">
        <source>You must select at least one category.</source>
        <target>You must select at least one category.</target>
        <note>Line: 592</note>
      </trans-unit>
      <trans-unit id="817c37b9c1f5cd4a450dad384e63e6c7">
        <source>Your filter</source>
        <target>Your filter</target>
        <note>Line: 696</note>
      </trans-unit>
      <trans-unit id="3185cefd67b575e582063148e4f15860">
        <source>was updated successfully.</source>
        <target>was updated successfully.</target>
        <note>Line: 700</note>
      </trans-unit>
      <trans-unit id="7ccab4d8de5d6b9bb61e99c7bba343ab">
        <source>was added successfully.</source>
        <target>was added successfully.</target>
        <note>Line: 701</note>
      </trans-unit>
      <trans-unit id="fe016d3b990c2a9dd72ab6b45892f2ae">
        <source>Settings saved successfully</source>
        <target>Settings saved successfully</target>
        <note>Line: 719</note>
      </trans-unit>
      <trans-unit id="0d07af70081a2421e2b2972609d699db">
        <source>Filter template deleted, categories updated (reverted to default Filter template).</source>
        <target>Filter template deleted, categories updated (reverted to default Filter template).</target>
        <note>Line: 734</note>
      </trans-unit>
      <trans-unit id="491f46aa6101560e9f1e0d55a063231b">
        <source>Filter template not found</source>
        <target>Filter template not found</target>
        <note>Line: 766</note>
      </trans-unit>
      <trans-unit id="a3868119dc6858db57127fd26e6f9656">
        <source>My template - %s</source>
        <target>My template - %s</target>
        <note>Line: 857</note>
      </trans-unit>
      <trans-unit id="32d2e6cd4bb1719c572ef470a3a525b6">
        <source>My template %s</source>
        <target>My template %s</target>
        <note>Line: 1233</note>
      </trans-unit>
      <trans-unit id="b3786b970611c1a3809dd51b630812a7">
        <source>"%s" is not a valid url</source>
        <target>"%s" is not a valid url</target>
        <note>Line: 1382</note>
      </trans-unit>
      <trans-unit id="9461ef0a8f8a2adbb3fa8ea0fda9949d">
        <source>You must select at least one page.</source>
        <target>You must select at least one page.</target>
        <note>Line: 608</note>
      </trans-unit>
      <trans-unit id="d4513f10f8edb4426a5723738e129861">
        <source>You must select at least one category if you want to use this filter template on category pages.</source>
        <target>You must select at least one category if you want to use this filter template on category pages.</target>
        <note>Line: 610</note>
      </trans-unit>
      <trans-unit id="3adbdb3ac060038aa0e6e6c138ef9873">
        <source>Category</source>
        <target>Category</target>
        <note>Line: 1727</note>
      </trans-unit>
      <trans-unit id="c0bd7654d5b278e65f21cf4e9153fdb4">
        <source>Manufacturer</source>
        <target>Manufacturer</target>
        <note>Line: 1731</note>
      </trans-unit>
      <trans-unit id="ec136b444eede3bc85639fac0dd06229">
        <source>Supplier</source>
        <target>Supplier</target>
        <note>Line: 1735</note>
      </trans-unit>
      <trans-unit id="9ff0635f5737513b1a6f559ac2bff745">
        <source>New products</source>
        <target>New products</target>
        <note>Line: 1739</note>
      </trans-unit>
      <trans-unit id="bd7929bc6bed5d8ec99db715a980ba04">
        <source>Best sales</source>
        <target>Best sales</target>
        <note>Line: 1743</note>
      </trans-unit>
      <trans-unit id="6de5d0b17afc50642850ff9486934f87">
        <source>Prices drop</source>
        <target>Prices drop</target>
        <note>Line: 1747</note>
      </trans-unit>
      <trans-unit id="13348442cc6a27032d2b4aa28b75a5d3">
        <source>Search</source>
        <target>Search</target>
        <note>Line: 1751</note>
      </trans-unit>
      <trans-unit id="bab956f96196bebabbfb693f920b1bc1">
        <source>(experimental)</source>
        <target>(experimental)</target>
        <note>Line: 1751</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/src/Form/Attribute/FormModifier.php" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="9e11e4b371570340ca07913bc4783a7a">
        <source>Meta title</source>
        <target>Meta title</target>
        <note>Line: 93</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/admin/_functions/show_limit.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="af605ea55ee39e54c444f217e346048f">
        <source>No limit</source>
        <target>No limit</target>
        <note>Line: 23</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/admin/_partials/categories-tree.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="5d9632c49fb1586eed7123afe2bd806f">
        <source>Categories used for this template:</source>
        <target>Categories used for this template:</target>
        <note>Line: 20</note>
      </trans-unit>
      <trans-unit id="78587049e12fb4f6b12e2bb045f2880a">
        <source>Categories selection is disabled because you have no categories or you are in a "all shops" context.</source>
        <target>Categories selection is disabled because you have no categories or you are in a "all shops" context.</target>
        <note>Line: 26</note>
      </trans-unit>
      <trans-unit id="6a84ae1d689983a9cc5fb6676eb46279">
        <source>Effective only if category pages are selected above.</source>
        <target>Effective only if category pages are selected above.</target>
        <note>Line: 29</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/admin/_partials/controllers.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="bd8da101b4d007b536f36eadc7f686e7">
        <source>Pages using this template:</source>
        <target>Pages using this template:</target>
        <note>Line: 20</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/admin/_partials/footer.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="dc3f85827350641490287c65c0c4ddf8">
        <source>You must select at least one filter</source>
        <target>You must select at least one filter</target>
        <note>Line: 31</note>
      </trans-unit>
      <trans-unit id="18c6120643596bd2626f3b0720b1df3a">
        <source>You must select at least one category</source>
        <target>You must select at least one category</target>
        <note>Line: 29</note>
      </trans-unit>
      <trans-unit id="de9bebfdc6b9fc0ea050a478f27cbb92">
        <source>You must select at least one page</source>
        <target>You must select at least one page</target>
        <note>Line: 30</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/admin/_partials/header.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="f8263d99054a4cdb3428196f078fa212">
        <source>Template name:</source>
        <target>Template name:</target>
        <note>Line: 20</note>
      </trans-unit>
      <trans-unit id="4284fda63513b7da70b5d8f032900580">
        <source>Only as a reminder</source>
        <target>Only as a reminder</target>
        <note>Line: 23</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/admin/_partials/messages.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="1f0f8b59397f34d499b181297714713a">
        <source>Warning! Your hosting provider is using the Suhosin patch for PHP, which limits the maximum number of fields allowed in a form:</source>
        <target>Warning! Your hosting provider is using the Suhosin patch for PHP, which limits the maximum number of fields allowed in a form:</target>
        <note>Line: 31</note>
      </trans-unit>
      <trans-unit id="9df9f71b2fc62b0ce48cbb8cb5671ee6">
        <source>for suhosin.post.max_vars.</source>
        <target>for suhosin.post.max_vars.</target>
        <note>Line: 33</note>
      </trans-unit>
      <trans-unit id="961e2bc7e3f570a3209546330de84a00">
        <source>for suhosin.request.max_vars.</source>
        <target>for suhosin.request.max_vars.</target>
        <note>Line: 34</note>
      </trans-unit>
      <trans-unit id="308c1cf98be9973142833c9a9dbbc2ef">
        <source>Please ask your hosting provider to increase the Suhosin limit to</source>
        <target>Please ask your hosting provider to increase the Suhosin limit to</target>
        <note>Line: 35</note>
      </trans-unit>
      <trans-unit id="5ad040870ae5ccaeb2d57e4d052163a4">
        <source>Warning! Your PHP configuration limits the maximum number of fields allowed in a form:</source>
        <target>Warning! Your PHP configuration limits the maximum number of fields allowed in a form:</target>
        <note>Line: 37</note>
      </trans-unit>
      <trans-unit id="5607dc45ebdfc0baaab64d944aa88f47">
        <source>for max_input_vars.</source>
        <target>for max_input_vars.</target>
        <note>Line: 38</note>
      </trans-unit>
      <trans-unit id="6e709babf4b5a0ed4194ad5240e1be2c">
        <source>Please ask your hosting provider to increase this limit to</source>
        <target>Please ask your hosting provider to increase this limit to</target>
        <note>Line: 39</note>
      </trans-unit>
      <trans-unit id="5d27fef01a54790de11db21f1ed47892">
        <source>%s at least, or you will have to edit the translation files manually.</source>
        <target>%s at least, or you will have to edit the translation files manually.</target>
        <note>Line: 41</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/admin/_partials/shops.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="c2d0bf5ad42279c519cdcb4a94eb46b6">
        <source>Choose shop association:</source>
        <target>Choose shop association:</target>
        <note>Line: 21</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/admin/add.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="cfbc982f8fb7a0cc3abb3c85c795ab41">
        <source>Sub-categories filter</source>
        <target>Sub-categories filter</target>
        <note>Line: 63</note>
      </trans-unit>
      <trans-unit id="cd50ff1c5332f9920acf8173c4aab425">
        <source>Product stock filter</source>
        <target>Product stock filter</target>
        <note>Line: 95</note>
      </trans-unit>
      <trans-unit id="048c1728a0a6cf36f56c9dcdd23d8a14">
        <source>Product condition filter</source>
        <target>Product condition filter</target>
        <note>Line: 156</note>
      </trans-unit>
      <trans-unit id="e581c019fe5c1b2aa8dfa18d93f88d13">
        <source>Product brand filter</source>
        <target>Product brand filter</target>
        <note>Line: 187</note>
      </trans-unit>
      <trans-unit id="cc72ed9534a489b5d2e5882735bf1364">
        <source>Product weight filter (slider)</source>
        <target>Product weight filter (slider)</target>
        <note>Line: 218</note>
      </trans-unit>
      <trans-unit id="0649bb392812f99ff6b0e2ba160675fa">
        <source>Product price filter (slider)</source>
        <target>Product price filter (slider)</target>
        <note>Line: 242</note>
      </trans-unit>
      <trans-unit id="4e0e25a7f0ffc6adce58b7fd3412cd35">
        <source>Product extras filter</source>
        <target>Product extras filter</target>
        <note>Line: 127</note>
      </trans-unit>
      <trans-unit id="51e17eed0057675de4bde1b34206bb12">
        <source>New filters template</source>
        <target>New filters template</target>
        <note>Line: 23</note>
      </trans-unit>
      <trans-unit id="88ec58dbbe7a8b727200696cfca4df3d">
        <source>You can drag and drop filters to adjust position</source>
        <target>You can drag and drop filters to adjust position</target>
        <note>Line: 36</note>
      </trans-unit>
      <trans-unit id="60266302eeda2ac9775c3a2036ae25ca">
        <source>Filters:</source>
        <target>Filters:</target>
        <note>Line: 36</note>
      </trans-unit>
      <trans-unit id="29da3cb8b65298a3e88f5041e9fb9761">
        <source>Total filters: %s</source>
        <target>Total filters: %s</target>
        <note>Line: 42</note>
      </trans-unit>
      <trans-unit id="379d3973e10dfd90c475060f31b9ae74">
        <source>Filter result limit:</source>
        <target>Filter result limit:</target>
        <note>Line: 350</note>
      </trans-unit>
      <trans-unit id="284fd1ee8a33e84e08699ba0bbc44943">
        <source>Filter style:</source>
        <target>Filter style:</target>
        <note>Line: 356</note>
      </trans-unit>
      <trans-unit id="4f8222964f9a317cef99dddc23a121bd">
        <source>Checkbox</source>
        <target>Checkbox</target>
        <note>Line: 359</note>
      </trans-unit>
      <trans-unit id="07a9ca8c8228dd3399141e228034fedf">
        <source>Radio button</source>
        <target>Radio button</target>
        <note>Line: 360</note>
      </trans-unit>
      <trans-unit id="5204077231fc7164e2269e96b584dd95">
        <source>Drop-down list</source>
        <target>Drop-down list</target>
        <note>Line: 361</note>
      </trans-unit>
      <trans-unit id="ce9bd6e7a7bfa2fa2d9e43bc4fcfeb8a">
        <source>List of ranges</source>
        <target>List of ranges</target>
        <note>Line: 250</note>
      </trans-unit>
      <trans-unit id="db6b86d05039c4f657a28647f8eb5140">
        <source>Attribute group: %name% (%count% attributes)</source>
        <target>Attribute group: %name% (%count% attributes)</target>
        <note>Line: 270</note>
      </trans-unit>
      <trans-unit id="788a474b0e55d612b3d500743c6251a0">
        <source>Attribute group: %name% (%count% attribute)</source>
        <target>Attribute group: %name% (%count% attribute)</target>
        <note>Line: 279</note>
      </trans-unit>
      <trans-unit id="ee59f74265cd7f85d0ad30206a1a89b0">
        <source>This group will allow user to select a color</source>
        <target>This group will allow user to select a color</target>
        <note>Line: 289</note>
      </trans-unit>
      <trans-unit id="4127fb33c1db9597f3c881ae057f1370">
        <source>Feature: %name% (%count% values)</source>
        <target>Feature: %name% (%count% values)</target>
        <note>Line: 329</note>
      </trans-unit>
      <trans-unit id="cadad7b648bb2b1d3f22421b00a14fd2">
        <source>Feature: %name% (%count% value)</source>
        <target>Feature: %name% (%count% value)</target>
        <note>Line: 338</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/admin/manage.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="df2bbc994d10995dcffdf96dbb7acbb1">
        <source>Indexes and caches</source>
        <target>Indexes and caches</target>
        <note>Line: 22</note>
      </trans-unit>
      <trans-unit id="ad3e7eb269d8ba0ac388267627f45b5a">
        <source>Indexing is in progress. Please do not leave this page</source>
        <target>Indexing is in progress. Please do not leave this page</target>
        <note>Line: 24</note>
      </trans-unit>
      <trans-unit id="5e2420d2318025812dc3e231ddb66b0b">
        <source>Index all missing prices</source>
        <target>Index all missing prices</target>
        <note>Line: 28</note>
      </trans-unit>
      <trans-unit id="9612e005e96ad32b8830be4d0377e7e6">
        <source>Rebuild entire price index</source>
        <target>Rebuild entire price index</target>
        <note>Line: 29</note>
      </trans-unit>
      <trans-unit id="fc11f4442332d8b86f01335df9d28444">
        <source>Build attributes and features indexes</source>
        <target>Build attributes and features indexes</target>
        <note>Line: 30</note>
      </trans-unit>
      <trans-unit id="79c0d6cba080dc90b01c887064c9fc2f">
        <source>Clear cache</source>
        <target>Clear cache</target>
        <note>Line: 31</note>
      </trans-unit>
      <trans-unit id="53795c3624ae2361363780589aa2aa42">
        <source>You can set a cron job that will rebuild price index using the following URL:</source>
        <target>You can set a cron job that will rebuild price index using the following URL:</target>
        <note>Line: 36</note>
      </trans-unit>
      <trans-unit id="e43b32b88c77e49f06144cd1ffaeba96">
        <source>You can set a cron job that will rebuild attribute index using the following URL:</source>
        <target>You can set a cron job that will rebuild attribute index using the following URL:</target>
        <note>Line: 41</note>
      </trans-unit>
      <trans-unit id="16349835364cf839e6670b0de7da6362">
        <source>A nightly rebuild is recommended.</source>
        <target>A nightly rebuild is recommended.</target>
        <note>Line: 47</note>
      </trans-unit>
      <trans-unit id="dc83eb2d8601743d8111c5150b93fc71">
        <source>Filters templates</source>
        <target>Filters templates</target>
        <note>Line: 52</note>
      </trans-unit>
      <trans-unit id="f7f19392da30e81c3abf433ce7b8ca38">
        <source>Created on</source>
        <target>Created on</target>
        <note>Line: 62</note>
      </trans-unit>
      <trans-unit id="06df33001c1d7187fdd81ea1f5b277aa">
        <source>Actions</source>
        <target>Actions</target>
        <note>Line: 63</note>
      </trans-unit>
      <trans-unit id="eb0728df77683ac0f7210ed0d4a18d62">
        <source>Do you really want to delete this filter template?</source>
        <target>Do you really want to delete this filter template?</target>
        <note>Line: 87</note>
      </trans-unit>
      <trans-unit id="058eeeba77f547f8a9a295a0efd4f6cd">
        <source>No filter template found.</source>
        <target>No filter template found.</target>
        <note>Line: 103</note>
      </trans-unit>
      <trans-unit id="ae2b83a081959fff7ab2e96f4ce972d1">
        <source>Add new template</source>
        <target>Add new template</target>
        <note>Line: 107</note>
      </trans-unit>
      <trans-unit id="2b2aaeeb1d226ef5566bfb781c66ffbd">
        <source>Enable cache system</source>
        <target>Enable cache system</target>
        <note>Line: 115</note>
      </trans-unit>
      <trans-unit id="8531c73de81b9ed94322dda7cf947daa">
        <source>Show the number of matching products</source>
        <target>Show the number of matching products</target>
        <note>Line: 137</note>
      </trans-unit>
      <trans-unit id="ee61c015043c79c1370fc14980dd27b9">
        <source>Show products from subcategories</source>
        <target>Show products from subcategories</target>
        <note>Line: 159</note>
      </trans-unit>
      <trans-unit id="dea2248d77db1c9f755f80ca9f874be8">
        <source>Show products only from default category</source>
        <target>Show products only from default category</target>
        <note>Line: 181</note>
      </trans-unit>
      <trans-unit id="75c30ac5e33d08c8cc95c5933c2cf543">
        <source>Works only if "Show products from subcategories" is off.</source>
        <target>Works only if "Show products from subcategories" is off.</target>
        <note>Line: 197</note>
      </trans-unit>
      <trans-unit id="a19399fa42f1ab059401a14b9f13eba1">
        <source>Category filter depth (0 for no limits, 1 by default)</source>
        <target>Category filter depth (0 for no limits, 1 by default)</target>
        <note>Line: 187</note>
      </trans-unit>
      <trans-unit id="3e652bd299bb3ee3d458c0dcc7fd706e">
        <source>Use tax to filter price</source>
        <target>Use tax to filter price</target>
        <note>Line: 215</note>
      </trans-unit>
      <trans-unit id="30b1f6f4369e3d0f7a0d50b5cb96aabd">
        <source>Use rounding to filter price</source>
        <target>Use rounding to filter price</target>
        <note>Line: 232</note>
      </trans-unit>
      <trans-unit id="459ae4ae4d73f1ed176e5fc8aa3af312">
        <source>Show unavailable, out of stock last</source>
        <target>Show unavailable, out of stock last</target>
        <note>Line: 249</note>
      </trans-unit>
      <trans-unit id="56fc8142961f1f3e9f9ec0c178881b69">
        <source>(in progress)</source>
        <target>(in progress)</target>
        <note>Line: 315</note>
      </trans-unit>
      <trans-unit id="8c83a87ac8ee57d9bcd79d1aa9243bc0">
        <source>URL indexing finished</source>
        <target>URL indexing finished</target>
        <note>Line: 316</note>
      </trans-unit>
      <trans-unit id="49afa0d9ad5c1f8bf5413b9dc8a252c9">
        <source>Attribute indexing finished</source>
        <target>Attribute indexing finished</target>
        <note>Line: 317</note>
      </trans-unit>
      <trans-unit id="9ea5bab5df9a3f3abaa64951daf07e9b">
        <source>URL indexing failed</source>
        <target>URL indexing failed</target>
        <note>Line: 318</note>
      </trans-unit>
      <trans-unit id="414301b329318b3e916c5b91b0ca9126">
        <source>Attribute indexing failed</source>
        <target>Attribute indexing failed</target>
        <note>Line: 319</note>
      </trans-unit>
      <trans-unit id="fa059e7a64ab37fe21b01a220b6c073f">
        <source>Price indexing finished</source>
        <target>Price indexing finished</target>
        <note>Line: 320</note>
      </trans-unit>
      <trans-unit id="b55143bb1f46af4207ea4b5eb8e844ed">
        <source>Price indexing failed</source>
        <target>Price indexing failed</target>
        <note>Line: 321</note>
      </trans-unit>
      <trans-unit id="7cf7d150dd287df0a8e17eeb4cf2161d">
        <source>(in progress, %s products price to index)</source>
        <target>(in progress, %s products price to index)</target>
        <note>Line: 322</note>
      </trans-unit>
      <trans-unit id="8524de963f07201e5c086830d370797f">
        <source>Loading...</source>
        <target>Loading...</target>
        <note>Line: 323</note>
      </trans-unit>
      <trans-unit id="eb9c805f7590679f0742ba0ea0a3e77f">
        <source>You selected -All categories-: all existing filter templates will be deleted. Is it OK?</source>
        <target>You selected -All categories-: all existing filter templates will be deleted. Is it OK?</target>
        <note>Line: 324</note>
      </trans-unit>
      <trans-unit id="2e465237d27547eb60889256909234e7">
        <source>Use Jquery UI slider</source>
        <target>Use Jquery UI slider</target>
        <note>Line: 266</note>
      </trans-unit>
      <trans-unit id="1adb03626a4e0c1aaa27bf37f0f1096b">
        <source>Switch this off only if your theme does not use Jquery UI slider. It is recommended to keep it on when using classic theme.</source>
        <target>Switch this off only if your theme does not use Jquery UI slider. It is recommended to keep it on when using classic theme.</target>
        <note>Line: 282</note>
      </trans-unit>
      <trans-unit id="b4f8e8e66907c50c52c0ad3bd31e29b0">
        <source>Default filter template for new categories</source>
        <target>Default filter template for new categories</target>
        <note>Line: 288</note>
      </trans-unit>
      <trans-unit id="9e80bb018eeeced7e38da900e5f47135">
        <source>If you want to automatically assign a filter template to new categories, select it here.</source>
        <target>If you want to automatically assign a filter template to new categories, select it here.</target>
        <note>Line: 298</note>
      </trans-unit>
      <trans-unit id="7ee2f2da463bebd7507e3e0a39de6e99">
        <source>We recommend to set regular cron tasks to manage the indexes and cache on daily/weekly basis.</source>
        <target>We recommend to set regular cron tasks to manage the indexes and cache on daily/weekly basis.</target>
        <note>Line: 36</note>
      </trans-unit>
      <trans-unit id="964f08f089bd198a3b5433045510556b">
        <source>Add missing products to price index:</source>
        <target>Add missing products to price index:</target>
        <note>Line: 38</note>
      </trans-unit>
      <trans-unit id="d5da9a4cccaf88f37b2e097a9d34a836">
        <source>Rebuild price index:</source>
        <target>Rebuild price index:</target>
        <note>Line: 40</note>
      </trans-unit>
      <trans-unit id="203b4e9a5b29bb6ed5aeb6d0692941ce">
        <source>Rebuild attribute index:</source>
        <target>Rebuild attribute index:</target>
        <note>Line: 42</note>
      </trans-unit>
      <trans-unit id="2707341ee97a7c74914e0d59adbcf5ab">
        <source>Flush block cache:</source>
        <target>Flush block cache:</target>
        <note>Line: 44</note>
      </trans-unit>
      <trans-unit id="5466dc891d678f772cc36162753e92fa">
        <source>This option caches filtering blocks, so the module does not have to query for matching products all the time. The cache is invalidated on every modification on your store. If you encounter some incosistencies, disable this cache or make sure to flush it if needed.</source>
        <target>This option caches filtering blocks, so the module does not have to query for matching products all the time. The cache is invalidated on every modification on your store. If you encounter some incosistencies, disable this cache or make sure to flush it if needed.</target>
        <note>Line: 131</note>
      </trans-unit>
      <trans-unit id="2829d57ed813ec86c807b7dde4fe140c">
        <source>Enable or disable display of matching products after filters. Disabling this won't bring any performance benefit, because matching products need to be calculated anyway.</source>
        <target>Enable or disable display of matching products after filters. Disabling this won't bring any performance benefit, because matching products need to be calculated anyway.</target>
        <note>Line: 153</note>
      </trans-unit>
      <trans-unit id="86c8e8e4e43a8e6d27907e9cde0b3a71">
        <source>Enable this, if you want to display products from subcategories, even if they are not specifically assigned to the currently browsed category.</source>
        <target>Enable this, if you want to display products from subcategories, even if they are not specifically assigned to the currently browsed category.</target>
        <note>Line: 175</note>
      </trans-unit>
      <trans-unit id="dc668816c4d715e1021b44a4823924a4">
        <source>Category filter depth</source>
        <target>Category filter depth</target>
        <note>Line: 203</note>
      </trans-unit>
      <trans-unit id="917b770ceaad28f806736a648ff6e0d6">
        <source>This option controls the behavior of category filter block - how deep children of the currently browsed category you want to display? The default value is 1 - only the direct children. Use 0 for unlimited depth.</source>
        <target>This option controls the behavior of category filter block - how deep children of the currently browsed category you want to display? The default value is 1 - only the direct children. Use 0 for unlimited depth.</target>
        <note>Line: 209</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/hook/attribute_form.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="e6b391a8d2c4d45902a23a8b6585703d">
        <source>URL</source>
        <target>URL</target>
        <note>Line: 21</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/hook/attribute_group_form.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="e919c0dcca631198a18993158182c96b">
        <source>When the Faceted Search module is enabled, you can get more detailed URLs by choosing the word that best represent this attribute. By default, PrestaShop uses the attribute's name, but you can change that setting using this field.</source>
        <target>When the Faceted Search module is enabled, you can get more detailed URLs by choosing the word that best represent this attribute. By default, PrestaShop uses the attribute's name, but you can change that setting using this field.</target>
        <note>Line: 44</note>
      </trans-unit>
      <trans-unit id="988d04f25d03c7753f5e4752a9397e79">
        <source>When the Faceted Search module is enabled, you can get more detailed page titles by choosing the word that best represent this attribute. By default, PrestaShop uses the attribute's name, but you can change that setting using this field.</source>
        <target>When the Faceted Search module is enabled, you can get more detailed page titles by choosing the word that best represent this attribute. By default, PrestaShop uses the attribute's name, but you can change that setting using this field.</target>
        <note>Line: 72</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/hook/feature_form.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="28034a200e932f22b324a4dda1bb9f64">
        <source><![CDATA[Invalid characters: <>;=#{}_]]></source>
        <target><![CDATA[Invalid characters: <>;=#{}_]]></target>
        <note>Line: 21</note>
      </trans-unit>
      <trans-unit id="0bcff42b5aed2b0e4501ed178e4f2510">
        <source>Indexable</source>
        <target>Indexable</target>
        <note>Line: 78</note>
      </trans-unit>
      <trans-unit id="4e495bf87859dbd3914919cccea139e2">
        <source>Use this attribute in URL generated by the Faceted Search module.</source>
        <target>Use this attribute in URL generated by the Faceted Search module.</target>
        <note>Line: 93</note>
      </trans-unit>
      <trans-unit id="81e6451dad5734120899da86f7da4e4e">
        <source>When the Faceted Search module is enabled, you can get more detailed URLs by choosing the word that best represent this feature. By default, PrestaShop uses the feature's name, but you can change that setting using this field.</source>
        <target>When the Faceted Search module is enabled, you can get more detailed URLs by choosing the word that best represent this feature. By default, PrestaShop uses the feature's name, but you can change that setting using this field.</target>
        <note>Line: 44</note>
      </trans-unit>
      <trans-unit id="78b4c0278638dd0fd4b29a05895d7891">
        <source>When the Faceted Search module is enabled, you can get more detailed page titles by choosing the word that best represent this feature. By default, PrestaShop uses the feature's name, but you can change that setting using this field.</source>
        <target>When the Faceted Search module is enabled, you can get more detailed page titles by choosing the word that best represent this feature. By default, PrestaShop uses the feature's name, but you can change that setting using this field.</target>
        <note>Line: 72</note>
      </trans-unit>
    </body>
  </file>
  <file original="modules/ps_facetedsearch/views/templates/hook/feature_value_form.tpl" source-language="en-US" target-language="en" datatype="plaintext">
    <body>
      <trans-unit id="b0aefa9a5ca20f89372b37c2c35c1d4b">
        <source>When the Faceted Search module is enabled, you can get more detailed URLs by choosing the word that best represent this feature's value. By default, PrestaShop uses the value's name, but you can change that setting using this field.</source>
        <target>When the Faceted Search module is enabled, you can get more detailed URLs by choosing the word that best represent this feature's value. By default, PrestaShop uses the value's name, but you can change that setting using this field.</target>
        <note>Line: 44</note>
      </trans-unit>
      <trans-unit id="0e6de50c32b9b10fd375ee52090b7f3e">
        <source>When the Faceted Search module is enabled, you can get more detailed page titles by choosing the word that best represent this feature's value. By default, PrestaShop uses the value's name, but you can change that setting using this field.</source>
        <target>When the Faceted Search module is enabled, you can get more detailed page titles by choosing the word that best represent this feature's value. By default, PrestaShop uses the value's name, but you can change that setting using this field.</target>
        <note>Line: 72</note>
      </trans-unit>
    </body>
  </file>
</xliff>
