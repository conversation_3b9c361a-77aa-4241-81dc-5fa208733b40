<!-- ✅ ✅ ✅ If the summary is not displayed correctly, open the module configuration page and click the "Troubleshooting" button, then Fix the templates then clear the cache ✅ -->


<div class="dp_cart dp_seven_cart"
     data-id_customization="2"
>
    <div class="dp_input_div dp_input_1">
                                                                                                                                                                                                                                                                                <span style="">
                                    <strong>Width:</strong>
                                                                    100 
                                                                                    </span>
                    <br>
                                                                        <br>
                    
        
        
            </div>

    </div>
