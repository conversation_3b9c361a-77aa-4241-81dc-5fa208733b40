<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Switch } from '$lib/components/ui/switch/index.js';
	// noinspection ES6UnusedImports
	import * as Card from '$lib/components/ui/card';
	// noinspection ES6UnusedImports
	import * as Alert from '$lib/components/ui/alert';
	import { superForm } from 'sveltekit-superforms';
	import { page } from '$app/state';
	import { form_action } from '$lib/php';
	import { toast } from 'svelte-sonner';
	import { z } from 'zod';
	import { zod } from 'sveltekit-superforms/adapters';
	import Icon from '@iconify/svelte';
	import Spinner from '$lib/components/ui/spinner/Spinner.svelte';
	import { _ } from '$lib/i18n/';
	import { SettingsSchema } from '$lib/schemas/settings';
	import { debounce } from '$lib/helpers';

	const { data } = $props();

	const { form, enhance, message, tainted, errors, submitting, validateForm, submit } = superForm(data.form, {
		SPA: true,
		validators: zod(SettingsSchema),
		resetForm: false,
		invalidateAll: false,
		async onSubmit({ formElement, cancel }) {
			const { valid } = await validateForm();
			if (valid) {
				toast.loading(_('Saving...'));
				const data: z.infer<typeof SettingsSchema> = await form_action({
					route: page.route.id,
					action: formElement.getAttribute('action'),
					data: $form
				});
				form.update(() => data);
				message.set(_('The settings have been updated'));
				toast.success(_('The settings have been updated'));
			}

			cancel();
		}
	});

	let debouncedSubmit = debounce(submit, 500);
</script>

<form method="POST" use:enhance>
	<Card.Content>
		<div class="flex flex-col gap-6">
			<div class="flex items-center space-x-2">
				<Switch bind:checked={$form.active} class={{'decorated': $form.active}} id="active" onclick={debouncedSubmit} />
				<Label for="active">{_('Enable the module for this product')}</Label>
			</div>
		</div>
	</Card.Content>

	<Card.Footer class="flex gap-2">
		<Button class="flex gap-2" disabled={!$tainted} type="submit">
			<Spinner loading={$submitting}>
				<Icon icon="mdi:content-save" />
			</Spinner>
			{_('Save')}
		</Button>
		<span class="text-green-500">{$message}</span>
	</Card.Footer>
</form>

<style>
  :global(.decorated span) {
    background: border-box url("/logo.png") center center no-repeat;
    background-size: cover;
  }
</style>
