@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
	--background: hsl(0 0% 100%) /* <- Wrap in HSL */;
	--foreground: hsl(240 10% 3.9%);
	--muted: hsl(240 4.8% 95.9%);
	--muted-foreground: hsl(240 3.8% 46.1%);
	--popover: hsl(0 0% 100%);
	--popover-foreground: hsl(240 10% 3.9%);
	--card: hsl(0 0% 100%);
	--card-foreground: hsl(240 10% 3.9%);
	--border: hsl(240 5.9% 90%);
	--input: hsl(240 5.9% 90%);
	--primary: hsl(240 5.9% 10%);
	--primary-foreground: hsl(0 0% 98%);
	--secondary: hsl(240 4.8% 95.9%);
	--secondary-foreground: hsl(240 5.9% 10%);
	--accent: hsl(240 4.8% 95.9%);
	--accent-foreground: hsl(240 5.9% 10%);
	--destructive: hsl(0 72.2% 50.6%);
	--destructive-foreground: hsl(0 0% 98%);
	--ring: hsl(240 10% 3.9%);
	--sidebar: hsl(0 0% 98%);
	--sidebar-foreground: hsl(240 5.3% 26.1%);
	--sidebar-primary: hsl(240 5.9% 10%);
	--sidebar-primary-foreground: hsl(0 0% 98%);
	--sidebar-accent: hsl(240 4.8% 95.9%);
	--sidebar-accent-foreground: hsl(240 5.9% 10%);
	--sidebar-border: hsl(220 13% 91%);
	--sidebar-ring: hsl(217.2 91.2% 59.8%);

	--radius: 0.5rem;
}

.dark {
	--background: hsl(240 10% 3.9%);
	--foreground: hsl(0 0% 98%);
	--muted: hsl(240 3.7% 15.9%);
	--muted-foreground: hsl(240 5% 64.9%);
	--popover: hsl(240 10% 3.9%);
	--popover-foreground: hsl(0 0% 98%);
	--card: hsl(240 10% 3.9%);
	--card-foreground: hsl(0 0% 98%);
	--border: hsl(240 3.7% 15.9%);
	--input: hsl(240 3.7% 15.9%);
	--primary: hsl(0 0% 98%);
	--primary-foreground: hsl(240 5.9% 10%);
	--secondary: hsl(240 3.7% 15.9%);
	--secondary-foreground: hsl(0 0% 98%);
	--accent: hsl(240 3.7% 15.9%);
	--accent-foreground: hsl(0 0% 98%);
	--destructive: hsl(0 62.8% 30.6%);
	--destructive-foreground: hsl(0 0% 98%);
	--ring: hsl(240 4.9% 83.9%);
	--sidebar: hsl(240 5.9% 10%);
	--sidebar-foreground: hsl(240 4.8% 95.9%);
	--sidebar-primary: hsl(224.3 76.3% 48%);
	--sidebar-primary-foreground: hsl(0 0% 100%);
	--sidebar-accent: hsl(240 3.7% 15.9%);
	--sidebar-accent-foreground: hsl(240 4.8% 95.9%);
	--sidebar-border: hsl(240 3.7% 15.9%);
	--sidebar-ring: hsl(217.2 91.2% 59.8%);
}

@theme inline {
	/* Radius (for rounded-*) */
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);

	/* Colors */
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-ring: var(--ring);
	--color-radius: var(--radius);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
}

@theme {
	--color-field-500: var(--color-blue-500);
	--color-field-600: var(--color-blue-600);
	--color-field-group-500: var(--color-fuchsia-500);
	--color-field-group-600: var(--color-fuchsia-600);
	--color-product-step-500: var(--color-green-500);
	--color-product-step-600: var(--color-green-600);
	--color-option-500: var(--color-orange-500);
	--color-option-600: var(--color-orange-600);
	--color-attribute-500: var(--color-slate-500);
	--color-attribute-600: var(--color-slate-600);
	--color-attribute-700: var(--color-slate-700);
	--color-feature-500: var(--color-violet-500);
	--color-feature-600: var(--color-violet-600);
	--color-feature-700: var(--color-violet-700);
	--color-ps-field-500: var(--color-pink-500);
	--color-ps-field-600: var(--color-pink-600);
	--color-ps-field-700: var(--color-pink-700);
	--color-database-500: var(--color-fuchsia-500);
	--color-database-600: var(--color-fuchsia-600);
	--color-database-700: var(--color-fuchsia-700);
	--color-symbol-500: var(--color-cyan-500);
	--color-symbol-600: var(--color-cyan-600);
	--color-symbol-700: var(--color-cyan-700);

    --color-error: var(--color-red-500);
    --color-warning: var(--color-yellow-500);
    --color-success: var(--color-green-500);
}

@layer base {
	* {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground pointer-events-auto! /* avoid caused by shadcn sheet */
		;
	}
}

.dp-away {
	position: fixed;
	top: -10000%;
	left: -10000%;
}

.field-btn {
	@apply bg-field-500 border-field-600 border normal-case text-white;

	&:hover {
		@apply bg-field-600 text-white;
	}

	&:active {
		@apply bg-field-500 text-white;
	}
}

.field-group-btn {
	@apply bg-field-group-500 border-field-group-600 border normal-case text-white;

	&:hover {
		@apply bg-field-group-600 text-white;
	}

	&:active {
		@apply bg-field-group-500 text-white;
	}
}

.product-step-btn {
	@apply bg-product-step-500 border-product-step-600 border normal-case text-white;

	&:hover {
		@apply bg-product-step-600 text-white;
	}

	&:active {
		@apply bg-product-step-500 text-white;
	}
}

.attribute-btn {
	@apply bg-attribute-500 border-attribute-600 border normal-case text-white;

	&:hover {
		@apply bg-attribute-600;
	}

	&:active {
		@apply bg-attribute-700;
	}
}

.feature-btn {
	@apply bg-feature-500 border-feature-600 border normal-case text-white;

	&:hover {
		@apply bg-feature-600;
	}

	&:active {
		@apply bg-feature-700;
	}
}

.ps-field-btn {
	@apply bg-ps-field-500 border-ps-field-600 border normal-case text-white;

	&:hover {
		@apply bg-ps-field-600;
	}

	&:active {
		@apply bg-ps-field-700;
	}
}

.database-btn {
	@apply bg-database-500 border-database-600 border normal-case text-white;

	&:hover {
		@apply bg-database-600;
	}

	&:active {
		@apply bg-database-700;
	}
}

.symbol-btn {
	@apply bg-symbol-500 border-symbol-600 border normal-case text-white;

	&:hover {
		@apply bg-symbol-600;
	}

	&:active {
		@apply bg-symbol-700;
	}
}

button.hidden-by-condition {
	@apply border! border-muted! bg-muted! normal-case! text-muted-foreground!;
}

kbd {
	@apply inline-flex min-h-[20px] items-center justify-center rounded-sm border border-gray-200 bg-white px-1.5 py-1 font-mono text-sm text-gray-800 shadow-[0px_2px_0px_0px_rgba(0,0,0,0.08)] dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-200 dark:shadow-[0px_2px_0px_0px_rgba(255,255,255,0.1)];
}
