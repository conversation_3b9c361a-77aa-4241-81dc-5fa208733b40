<script lang="ts">
	// noinspection ES6UnusedImports
	import * as Sheet from '$lib/components/ui/sheet/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { superForm } from 'sveltekit-superforms';
	import { url } from '$lib/urls';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { FieldTypes } from '$lib/fields/types';
	import InputSettings from './components/InputSettings.svelte';
	import { _ } from '$lib/i18n';
	import Spinner from '$lib/components/ui/spinner/Spinner.svelte';
	import Icon from '@iconify/svelte';
	import { form_action } from '$lib/php';
	import { page } from '$app/state';
	import { toast } from 'svelte-sonner';
	import CheckboxSettings from './components/CheckboxSettings.svelte';
	import TextSettings from './components/TextSettings.svelte';
	import DateSettings from './components/DateSettings.svelte';
	import ImageSettings from './components/ImageSettings.svelte';
	import FileSettings from './components/FileSettings.svelte';
	import FeatureSettings from './components/FeatureSettings.svelte';
	import ColorPickerSettings from './components/ColorPickerSettings.svelte';
	import CustomFieldSettings from './components/CustomFieldSettings.svelte';
	import CountrySettings from './components/CountrySettings.svelte';
	import DropdownSettings from './components/DropdownSettings.svelte';
	import RadioSettings from './components/RadioSettings.svelte';
	import ImageListSettings from './components/ImageListSettings.svelte';
	import HtmlSettings from './components/HtmlSettings.svelte';
	import ErrorSettings from './components/ErrorSettings.svelte';
	import { FieldConfig } from '$lib/fields/config';
	import FontSettings from './components/FontSettings.svelte';

	let { data } = $props();
	let { field, units } = $derived(data);

	const superform = superForm(data.form, {
		dataType: 'json',
		resetForm: false,
		invalidateAll: false,
		async onSubmit({ cancel }) {
			const res = await form_action(
				{
					route: page.route.id,
					action: 'update',
					data: {
						settings: $form
					}
				},
				{
					id_field: field.id
				}
			);
			if (res) {
				toast.success(_('Field settings updated successfully'));
			}
			await goto(url('/fields'), {
				noScroll: true
			});
			cancel();
		}
	});

	const { form, enhance, tainted, submitting } = superform;

	let config = $derived(FieldConfig.get(+field.type || FieldTypes._DP_INPUT_));

	let settings_components = {
		[FieldTypes._DP_INPUT_]: InputSettings,
		[FieldTypes._DP_SLIDER_]: InputSettings,

		[FieldTypes._DP_DROPDOWN_]: DropdownSettings,
		[FieldTypes._DP_RADIO_]: RadioSettings,
		[FieldTypes._DP_THUMBNAILS_]: ImageListSettings,

		[FieldTypes._DP_CHECKBOX_]: CheckboxSettings,
		[FieldTypes._DP_SWITCH_]: CheckboxSettings,

		[FieldTypes._DP_TEXT_]: TextSettings,
		[FieldTypes._DP_TEXTAREA_]: TextSettings,

		[FieldTypes._DP_DATE_]: DateSettings,
		[FieldTypes._DP_IMAGE_]: ImageSettings,
		[FieldTypes._DP_FILE_]: FileSettings,
		[FieldTypes._DP_FEATURE_]: FeatureSettings,
		[FieldTypes._DP_COLORPICKER_]: ColorPickerSettings,
		[FieldTypes._DP_HTML_]: HtmlSettings,
		[FieldTypes._DP_ERROR_]: ErrorSettings,
		[FieldTypes._DP_CUSTOM_]: CustomFieldSettings,
		[FieldTypes._DP_PREVIEW_]: InputSettings,
		[FieldTypes._DP_COUNTRY_]: CountrySettings,
		[FieldTypes._DP_FONT_]: FontSettings
	};

	let SettingsComponent = $derived(settings_components[field.type] || InputSettings);

	let open = $state(false);
	onMount(() => {
		open = true;
	});
</script>

<Sheet.Root bind:open onOpenChangeComplete={() => !open && goto(url('/fields'), { noScroll: true })}>
	<Sheet.Content class="w-[800px] overflow-scroll sm:max-w-none">
		<Sheet.Header>
			<Sheet.Title>
				<div class="flex justify-between gap-4 pr-10">
					<div class="flex items-center gap-2">
						<Button onclick={() => (open = false)} size="sm" variant="outline">
							<Icon icon="mdi:arrow-left" />
						</Button>
						<div>
							{_('Edit field')}: {field.name}
							<Sheet.Description>
								{_('Field type')}: {data.field_types.find((t) => t.type === field.type)?.label}
							</Sheet.Description>
						</div>
					</div>
					{#if config?.options}
						<Button href={url(`/fields/${field.id}/options`)} size="sm">
							<Icon icon="mdi:format-list-bulleted" />
							{_('Field options')}
							<Icon icon="mdi:arrow-right" />
						</Button>
					{/if}
				</div>
			</Sheet.Title>
		</Sheet.Header>
		<form method="post" target="dialog" use:enhance>
			<div class="flex flex-col gap-4 py-4">
				<SettingsComponent {superform} {units} />
			</div>
			<Sheet.Footer class="sm:justify-start">
				<Button class="flex gap-2" disabled={!$tainted} type="submit">
					<Spinner loading={$submitting}>
						<Icon icon="mdi:content-save" />
					</Spinner>
					{_('Save changes')}
				</Button>
				<Button class="flex gap-2" onclick={() => (open = false)} variant="outline">
					<Icon icon="mdi:arrow-left" />
					{_('Cancel')}
				</Button>
			</Sheet.Footer>
		</form>
	</Sheet.Content>
</Sheet.Root>
