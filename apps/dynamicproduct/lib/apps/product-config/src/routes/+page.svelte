<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Switch } from '$lib/components/ui/switch/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	// noinspection ES6UnusedImports
	import * as Card from '$lib/components/ui/card';
	// noinspection ES6UnusedImports
	import * as Alert from '$lib/components/ui/alert';
	import { superForm } from 'sveltekit-superforms';
	import { page } from '$app/state';
	import { form_action } from '$lib/php';
	import { toast } from 'svelte-sonner';
	import { z } from 'zod/v4';
	import { zod4 as zod } from 'sveltekit-superforms/adapters';
	import Icon from '@iconify/svelte';
	import Spinner from '$lib/components/ui/spinner/Spinner.svelte';
	import { _ } from '$lib/i18n/';
	import { SettingsSchema } from '$lib/schemas/settings';
	import { debounce } from '$lib/helpers';

	const { data } = $props();

	const { form, enhance, message, tainted, errors, submitting, validateForm, submit } = superForm(
		data.form,
		{
			SPA: true,
			validators: zod(SettingsSchema),
			resetForm: false,
			invalidateAll: false,
			async onSubmit({ formElement, cancel }) {
				const { valid } = await validateForm();
				if (valid) {
					toast.loading(_('Saving...'));
					const data: z.infer<typeof SettingsSchema> = await form_action({
						route: page.route.id,
						action: formElement.getAttribute('action'),
						data: $form
					});
					form.update(() => data);
					message.set(_('The settings have been updated'));
					toast.success(_('The settings have been updated'));
				}

				cancel();
			}
		}
	);

	let debouncedSubmit = debounce(submit, 500);
</script>

<form method="POST" use:enhance>
	<Card.Content>
		<div class="flex flex-col gap-6">
			<div class="flex items-center space-x-2">
				<Switch
					bind:checked={$form.active}
					class={{ decorated: $form.active }}
					id="active"
					onclick={debouncedSubmit}
				/>
				<Label for="active">{_('Enable the module for this product')}</Label>
			</div>

			<div class="flex items-center space-x-2">
				<Switch bind:checked={$form.required} id="required" onclick={debouncedSubmit} />
				<Label for="required">{_('Required customization')}</Label>
			</div>

			<div class="items-top flex space-x-2">
				<Switch bind:checked={$form.recalc} id="recalc" onclick={debouncedSubmit} />
				<div class="grid gap-1.5 leading-none">
					<Label for="recalc">{_('Recalculate price in cart when quantity changes')}</Label>
					<p class="text-muted-foreground max-w-xs text-sm">
						{_('Enable if the price depends on the cart quantity')}
					</p>
				</div>
			</div>

			{#if $form.recalc}
				<div class="items-top flex space-x-2 pl-8">
					<Switch onclick={debouncedSubmit} bind:checked={$form.always_recalc} id="always_recalc" />
					<div class="grid gap-1.5 leading-none">
						<Label for="always_recalc">{_('Recalculate price in cart after each page view')}</Label>
						<p class="text-muted-foreground max-w-xs text-sm">
							{_('Not recommended as it can slow down the cart page')}
						</p>
					</div>
				</div>
			{/if}

			<Card.Root class="w-fit">
				<Card.Content>
					<div class="flex flex-col gap-6">
						<div class="flex flex-col gap-2">
							<div class="flex gap-4">
								<div class="flex w-full max-w-48 flex-col gap-1.5">
									<Label for="displayed_price">{_('Displayed price')}</Label>
									<Input
										bind:value={$form.displayed_price}
										id="displayed_price"
										onchange={submit}
									/>
								</div>
								<div class="flex w-full max-w-48 flex-col gap-1.5">
									<Label for="displayed_price_label">{_('Price unit')}</Label>
									<Input
										bind:value={$form.displayed_price_label}
										id="displayed_price_label"
										onchange={submit}
										placeholder={_('Per kilo, per litre')}
									/>
								</div>
							</div>
							<p class="text-muted-foreground max-w-xs text-sm">
								{_('You can configure a displayed price if your product has a price of 0')}
							</p>
							{#if $errors.displayed_price}
								<Alert.Root variant="destructive">{$errors.displayed_price}</Alert.Root>
							{/if}
						</div>

						<div class="flex items-center space-x-2">
							<Switch
								bind:checked={$form.display_starting_from}
								id="display_starting_from"
								onclick={debouncedSubmit}
							/>
							<Label for="display_starting_from">
								{_('Display the "Starting from" label in the category page')}
							</Label>
						</div>

						<div class="items-top flex space-x-2">
							<Switch
								bind:checked={$form.display_dynamic_price}
								id="display_dynamic_price"
								onclick={debouncedSubmit}
							/>
							<div class="grid gap-1.5 leading-none">
								<Label for="display_dynamic_price">
									{_('Display the calculated price in the category page')}
								</Label>
								<p class="text-muted-foreground max-w-xs text-sm">
									{_(
										'Applies only if the product has a price of 0. You may need to clear the cache after changing this setting'
									)}
								</p>
							</div>
						</div>

						<div class="flex items-center space-x-2">
							<Switch
								bind:checked={$form.display_customization_cost}
								id="display_customization_cost"
								onclick={debouncedSubmit}
							/>
							<Label for="display_customization_cost">
								{_('Display customization cost in customization summary')}
							</Label>
						</div>
					</div>
				</Card.Content>
			</Card.Root>

			<div class="flex items-center space-x-2">
				<Switch bind:checked={$form.display_weight} id="display_weight" onclick={debouncedSubmit} />
				<Label for="display_weight">{_('Display weight to customers')}</Label>
			</div>

			<div class="flex items-center space-x-2">
				<Switch bind:checked={$form.hide_qty} id="hide_qty" onclick={debouncedSubmit} />
				<Label for="hide_qty">{_('Hide quantity input')}</Label>
			</div>

			<div class="items-top flex space-x-2">
				<Switch bind:checked={$form.multiply_price} id="multiply_price" onclick={debouncedSubmit} />
				<div class="grid gap-1.5 leading-none">
					<Label for="multiply_price">{_('Multiply price & weight by quantity')}</Label>
					<p class="text-muted-foreground max-w-xs text-sm">
						{_(
							'If activated, the displayed price & weight on the product page will be multiplied by the quantity'
						)}
					</p>
				</div>
			</div>

			<div class="flex items-center space-x-2">
				<Switch bind:checked={$form.allow_save} id="allow_save" onclick={debouncedSubmit} />
				<Label for="allow_save">{_('Allow saving customization to profile')}</Label>
			</div>

			<div class="flex items-center space-x-2">
				<Switch bind:checked={$form.split_summary} id="split_summary" onclick={debouncedSubmit} />
				<Label for="split_summary">{_('Split summary by groups')}</Label>
			</div>
		</div>
	</Card.Content>

	<Card.Footer class="flex gap-2">
		<Button class="flex gap-2" disabled={!$tainted} type="submit">
			<Spinner loading={$submitting}>
				<Icon icon="mdi:content-save" />
			</Spinner>
			{_('Save')}
		</Button>
    <span class="text-success">{$message}</span>
	</Card.Footer>
</form>

<style>
	:global(.decorated span) {
		background: border-box url('/logo.png') center center no-repeat;
		background-size: cover;
	}
</style>
