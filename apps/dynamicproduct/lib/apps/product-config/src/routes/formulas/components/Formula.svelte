<script lang="ts">
  import CodeMirror from '$lib/components/formula/CodeMirror.svelte'
  import FieldBtn from '$lib/components/formula/FieldBtn.svelte'
  import SymbolBtn from '$lib/components/formula/SymbolBtn.svelte'
  import {type SvelteComponent} from 'svelte'
  import {highlight} from '$lib/editor/highlighter'
  import {browser} from '$app/environment'
  import {z} from 'zod/v4'
  import type {SuperForm} from 'sveltekit-superforms'
  import {generateSymbols} from '$lib/formula/symbols'
  import type {FieldWithLang} from '@tunisoft/dynamicproduct/types/field'
  import {FormulasSchema} from '$lib/schemas/formulas.js'
  import Spinner from '$lib/components/ui/spinner/Spinner.svelte'
  import Icon from '@iconify/svelte'
  import {Button, buttonVariants} from '$lib/components/ui/button'
  import {_} from '$lib/i18n'
  // noinspection ES6UnusedImports
  import * as Dialog from '$lib/components/ui/dialog/index.js'
  import type {FormulaField} from '$lib/types'
  import {Label} from '$lib/components/ui/label'

  let {
    superform,
    errors,
    id_formula,
    fields,
    attributes,
    features,
    ps_fields,
    databases,
    open = $bindable({})
  }: {
    superform: SuperForm<z.infer<typeof FormulasSchema>>;
    errors: Record<number, string>;
    id_formula: number;
    fields: FieldWithLang[];
    attributes: FormulaField[];
    features: FormulaField[];
    ps_fields: FormulaField[];
    databases: FormulaField[];
    open: Record<number, boolean>;
  } = $props()

  const {form, tainted, submitting} = superform
  let {formula} = $derived($form.formulas[id_formula])

  let editor = $state<SvelteComponent | undefined>(undefined)
  let show_more = $state(false)
</script>

<Dialog.Root
    bind:open={open[id_formula]}
    onOpenChange={(open) => {
		if (open) {
			$form.formulas[id_formula].editable_formula = $form.formulas[id_formula].formula;
			$form.action_value = id_formula.toString();
		}
	}}
>
  <Dialog.Trigger
      class="{buttonVariants({ variant: 'outline' })} justify-start! {!formula &&
			'text-muted-foreground'}"
      data-testid="edit-formula"
      type="button"
  >
    {#if !browser}
      <span>{formula}</span>
    {:else if formula}
      <span class="font-mono">{@html highlight(formula)}</span>
    {:else}
      <span>{_('Click to edit formula')}</span>
    {/if}
  </Dialog.Trigger>
  <Dialog.Content class="sm:max-w-4xl">
    <Dialog.Header>
      <Dialog.Title>{_('Edit formula')}</Dialog.Title>
      <Dialog.Description>
        {_('Click the fields below to insert them into the formula')}
      </Dialog.Description>
    </Dialog.Header>
    <div class="grid gap-4">
      <div class="text-error flex items-center gap-1 text-sm">
        {#if errors[id_formula]}
          <Icon icon="ic:outline-error-outline"/>
          {errors[id_formula]}
        {:else}
          &nbsp;
        {/if}
      </div>
      <div class="form-control relative">
        <CodeMirror
            bind:code={$form.formulas[id_formula].editable_formula}
            bind:this={editor}
            fields={fields.filter((f) => f.name).map((f) => f.name)}
            form="form"
            name="code"
            onInit={(editorView) => {
						// Set cursor at end of text
						const docLength = editorView.state.doc.length;
						editorView.dispatch({
							selection: { anchor: docLength, head: docLength }
						});
						editorView.focus();
					}}
            tables={databases.filter((t) => t.name).map((t) => t.name)}
        />
      </div>
      <div class="flex flex-row flex-wrap gap-2">
        {#each generateSymbols(['+', '-', 'x', '/', '(', ')']) as [text, symbol] (symbol)}
          <SymbolBtn {text} onclick={() => editor?.insertSymbol(symbol)}/>
        {/each}
      </div>
      <div>
        <Label>{_('Fields')}</Label>
        <div class="flex flex-row flex-wrap gap-2">
          {#each fields.filter((f) => f.name) as field (field.id)}
            <FieldBtn {field} {editor}/>
          {/each}
          <Button
              class="field-btn bg-blue-700! text-white!"
              onclick={() => (show_more = !show_more)}
              type="button"
              variant="outline"
          >
            {#if show_more}
              {_('Show less')}
              <Icon icon="mdi:chevron-up"/>
            {:else}
              {_('Show more')}
              <Icon icon="mdi:chevron-down"/>
            {/if}
          </Button>
        </div>
      </div>
      {#if show_more}
        <div>
          {#if ps_fields.length}
            <div>
              <Label>{_('PrestaShop fields')}</Label>
              <div class="flex flex-row flex-wrap gap-2">
                {#each ps_fields as {name, label} (name)}
                  <Button
                      class="ps-field-btn cursor-pointer"
                      onclick={() => editor?.insertSymbol(`"[${name}]"`)}
                      type="button"
                      variant="outline"
                  >
                    {label}
                  </Button>
                {/each}
              </div>
            </div>
          {/if}
          {#if databases.length}
            <div>
              <Label>{_('Databases')}</Label>
              <div class="flex flex-row flex-wrap gap-2">
                {#each databases as {name, label} (name)}
                  <Button
                      class="database-btn cursor-pointer"
                      onclick={() => editor?.insertSymbol(`GRID("${name}", [row], [column], 0)`)}
                      type="button"
                      variant="outline"
                  >
                    {label}
                  </Button>
                {/each}
              </div>
            </div>
          {/if}
          {#if attributes.length}
            <div>
              <Label>{_('Attributes')}</Label>
              <div class="flex flex-row flex-wrap gap-2">
                {#each attributes as {name, label} (name)}
                  <Button
                      class="attribute-btn cursor-pointer"
                      onclick={() => editor?.insertSymbol(`"[${name}]"`)}
                      type="button"
                      variant="outline"
                  >
                    {label}
                  </Button>
                {/each}
              </div>
            </div>
          {/if}
          {#if features.length}
            <div>
              <Label>{_('Features')}</Label>
              <div class="flex flex-row flex-wrap gap-2">
                {#each features as {name, label} (name)}
                  <Button
                      class="feature-btn cursor-pointer"
                      onclick={() => editor?.insertSymbol(`"[${name}]"`)}
                      type="button"
                      variant="outline"
                  >
                    {label}
                  </Button>
                {/each}
              </div>
            </div>
          {/if}
        </div>
      {/if}
    </div>
    <Dialog.Footer>
      <Button
          data-ctrl-enter
          disabled={!$tainted?.formulas?.[id_formula]}
          form="form"
          name="id_formula"
          type="submit"
          value={id_formula}
      >
        <Spinner loading={$submitting}>
          <Icon icon="mdi:content-save"/>
        </Spinner>
        Save
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
