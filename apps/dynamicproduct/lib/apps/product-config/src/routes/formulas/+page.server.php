<?php

use DynamicProduct\classes\helpers\DynamicFieldsHelper;
use DynamicProduct\classes\helpers\ProductHelper;
use DynamicProduct\classes\models\DynamicEquation;
use DynamicProduct\classes\models\DynamicField;

/**
 * @param DynamicProduct $module
 * @param Context $context
 *
 * @return array
 */
function load(DynamicProduct $module, Context $context)
{
    $id_product = (int) Tools::getValue('id_product');

    return [
        'formulas' => [
            DynamicEquation::_DP_PRICE_EQ_ => DynamicEquation::getPriceEquation($id_product),
            DynamicEquation::_DP_WEIGHT_EQ_ => DynamicEquation::getWeightEquation($id_product),
            DynamicEquation::_DP_QUANTITY_EQ_ => DynamicEquation::getQuantityEquation($id_product),
            DynamicEquation::_DP_COST_EQ_ => DynamicEquation::getCostEquation($id_product),
        ],
        'fields' => DynamicField::getFieldRowsByProduct($id_product),
        'attributes' => ProductHelper::getProductAttributeFields($id_product),
        'features' => ProductHelper::getProductFeatureFields($id_product),
        'ps_fields' => ProductHelper::getPrestaShopFields(),
        'databases' => ProductHelper::getProductDatabaseFields(),
    ];
}

$actions = [
    'update' => function (DynamicProduct $module) {
        $id_product = (int) Tools::getValue('id_product');
        $formulas = Tools::getValue('formulas');
        $id_formula = (int) Tools::getValue('action_value');

        $formula = $formulas[$id_formula];

        $field_names = DynamicFieldsHelper::getFieldsNames($id_product);
        $validation = DynamicEquation::checkFormula($id_product, $formula['editable_formula'], $field_names);
        if ($validation !== true) {
            return [
                'error' => true,
                'message' => $validation,
            ];
        }

        $equation = DynamicEquation::getEquationByIdFormula($id_product, $id_formula);
        $equation->formula = $formula['editable_formula'];
        $equation->save();

        return [
            'formulas' => [
                DynamicEquation::_DP_PRICE_EQ_ => DynamicEquation::getPriceEquation($id_product),
                DynamicEquation::_DP_WEIGHT_EQ_ => DynamicEquation::getWeightEquation($id_product),
                DynamicEquation::_DP_QUANTITY_EQ_ => DynamicEquation::getQuantityEquation($id_product),
                DynamicEquation::_DP_COST_EQ_ => DynamicEquation::getCostEquation($id_product),
            ],
        ];
    },
];
