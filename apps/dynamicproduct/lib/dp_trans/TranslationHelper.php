<?php

namespace DynamicProduct\lib\dp_trans;

class TranslationHelper
{
    /** @var \DynamicProduct */
    public $module;
    /** @var \Context */
    public $context;

    public function __construct($module, $context)
    {
        $this->module = $module;
        $this->context = $context;
    }

    public function getAdminTranslations()
    {
        $source = 'TranslationHelper';

        return [
            // start admin 1945754001
            '449032306' => $this->module->l('Please select a product first', $source),
            '970976852' => $this->module->l('Customization added successfully', $source),
            '-1076613361' => $this->module->l('Please add a customization first', $source),
            '-1893379118' => $this->module->l('Loading...', $source),
            // end admin
        ];
    }
}
