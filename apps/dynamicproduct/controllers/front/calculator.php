<?php

/* @noinspection PhpUnusedPrivateMethodInspection */

use DynamicProduct\classes\controllers\front\DynamicFrontController;
use DynamicProduct\classes\DynamicTools;
use DynamicProduct\classes\helpers\DynamicCalculatorHelper;
use DynamicProduct\classes\models\DynamicInputField;
use DynamicProduct\classes\models\DynamicProductConfig;

/** @noinspection PhpUnused */
class DynamicProductCalculatorModuleFrontController extends DynamicFrontController
{
    /** @noinspection PhpUnused */
    protected function processCalculateResult()
    {
        $calculator_helper = new DynamicCalculatorHelper($this->module, $this->context);

        $fields = Tools::getValue('fields');
        $adapter_data = Tools::getValue('adapter_data');

        $input_fields = [];
        $fields_visibility = [];
        $grouped_fields = [];
        $product_steps = [];

        $calculator_helper->resetDebugMessages();

        try {
            list($input_fields, $fields_visibility, $grouped_fields) = DynamicInputField::getInputFieldsFromData(
                $this->id_product,
                $this->id_attribute,
                $fields,
                empty($fields['changed']['value']) ? DynamicInputField::LOAD_ALL : DynamicInputField::LOAD_NONE
            );
        } catch (Exception $e) {
            if (_PS_MODE_DEV_) {
                throw $e;
            }

            $this->respond([
                'error' => 1,
                'message' => DynamicTools::reportException($e),
            ]);
        }

        try {
            $calculation = $calculator_helper->processCalculation(
                $this->id_product,
                $this->id_attribute,
                $input_fields,
                $fields_visibility,
                $adapter_data
            );
        } catch (Exception $e) {
            if (_PS_MODE_DEV_) {
                throw $e;
            }
            $calculation = [
                'input_fields' => $input_fields,
                'visibility' => $fields_visibility,
                'debug_messages' => DynamicProduct::$debug_messages,
            ];
            $this->respond([
                'error' => 1,
                'message' => DynamicTools::reportException($e),
            ]);
        }

        $calculation['grouped_fields'] = $grouped_fields;

        $product_config = DynamicProductConfig::getByProduct($this->id_product);
        $calculation['product_steps'] = $product_config->enable_steps ? $product_steps : [];
        $this->respond($calculation);
    }
}
