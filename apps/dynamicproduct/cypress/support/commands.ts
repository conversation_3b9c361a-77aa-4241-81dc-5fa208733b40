/// <reference types="cypress" />
// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
//

declare global {
	// eslint-disable-next-line @typescript-eslint/no-namespace
	namespace Cypress {
		interface Chainable {
			import(spec: string): Chainable;

			backend(url: string): Chainable;

			debugSession(): Chainable;

			success(alias: string): Chainable;

			getByTestId: typeof cy.get;
			getByName: typeof cy.get;
		}
	}
}

Cypress.Commands.add('import', (spec: string) => {
	return cy.task('import', { spec });
});

// @ts-expect-error types are hard
Cypress.Commands.add('getByTestId', (selector, options) => {
	return cy.get(`[data-testid="${selector}"]`, options);
});

// @ts-expect-error types are hard
Cypress.Commands.add('getByName', (selector, options) => {
	return cy.get(`[name="${selector}"]`, options);
});

Cypress.Commands.add('debugSession', () => {
	cy.session('debug', () => {
		cy.setCookie('XDEBUG_SESSION', 'PHPSTORM');
	});
});

Cypress.Commands.add('backend', (url) => {
	cy.request({
		url: 'http://localhost/dev/',
		failOnStatusCode: false
	}).then((response) => {
		if (response.status === 200) {
			cy.visit(`http://localhost/dev${url}`, {});
		} else {
			cy.visit(
				`http://localhost/prestashop/test/dynamic/modules/dynamicproduct/lib/apps/product-config/build${url}`
			);
		}
	});
});

Cypress.Commands.add('success', (alias) => {
	cy.wait(alias)
		.its('response.statusCode')
		.should('eq', 200)
		.and('response.body')
		.should('not.have.a.property', 'error');
});

export {};
