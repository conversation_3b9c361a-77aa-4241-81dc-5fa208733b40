// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies <PERSON><PERSON>.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';
import 'cypress-watch-and-reload/support';
import 'cypress-real-events';

// Alternatively you can use CommonJS syntax:
// require('./commands')

beforeEach(function () {
	cy.debugSession();
	// disable <PERSON><PERSON>'s default behavior of logging all XMLHttpRequests and fetches to the Command Log
	cy.intercept('GET', /.*/, { log: false });
	// disable Cypress's default behavior of logging all XMLHttpRequests and fetches to the Command Log
	cy.intercept('POST', /\/(undefined|generated|@fs|@vite|node_modules)\//, {
		log: false
	});

	cy.intercept('POST', /dynamicproduct\/calculator/).as('calculator');
	cy.intercept('POST', /ps_shoppingcart\/ajax/).as('cart');

	cy.intercept('POST', /DynamicBackend/).as('backend');

	// cy.import('base');
});
