describe('admin intervals', () => {
	it('add field after current', () => {
		cy.import('admin/intervals');
		cy.backend('/?initial_path=intervals');

		cy.getByTestId('add-interval').click();
		cy.success('@backend');
		cy.getByTestId('interval').should('exist');

		cy.getByTestId('condition').click();
		cy.wait(500);
		cy.getByTestId('field').click();
		cy.get(`[data-select-item]`).contains('price_m2').click();
		cy.get('#max').clear().type('100');
		cy.get(`[name=update-interval-condition]`).click();
		cy.wait('@backend');
		cy.getByTestId('condition').should('contain.text', 'price_m2: 0 → 100');

		cy.getByTestId('formula').click();
		cy.get('.cm-content').focus().realType('10');
		cy.wait(100);
		cy.get(`[name=update]`).click();
		cy.getByTestId('formula').should('contain.text', '10');
	});
});
