describe('formulas', () => {
	it('validate a formula', () => {
		cy.import('admin/fields');
		cy.backend('/?initial_path=formulas');
		cy.getByTestId('edit-formula').eq(0).click();
		cy.get('.cm-content').focus().realType(' +');
		cy.get('button').contains('Save').click();
		cy.contains(
			'Sub expression "[width] * [height] +" in "[width] * [height] +" is not valid.'
		).should('be.visible');
	});
});
