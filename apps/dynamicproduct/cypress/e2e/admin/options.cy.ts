describe('field options', () => {
	it('adds font options', () => {
		cy.import('admin/fonts');
		cy.backend('/?initial_path=fields/1/options');
		cy.getByTestId('font-file').eq(0).selectFile(['cypress/fixtures/font1.ttf'], {
			force: true
		});
		cy.wait('@backend');
		cy.wait(1000);
		cy.getByTestId('font-file').eq(1).selectFile(['cypress/fixtures/font2.ttf'], {
			force: true
		});
		cy.getByTestId('font-image').should('have.length', 2);
	});
});
