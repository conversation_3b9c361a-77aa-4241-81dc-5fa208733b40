describe('grids', () => {
	it('create a new grid', () => {
		cy.import('admin/grids');
		cy.backend('/?initial_path=grids');

		cy.getByName('add').click()
		cy.getByTestId('value').eq(0).clear().type('10')
		cy.contains('Save grid').click()
		cy.success('@backend')
		cy.getByTestId('value').eq(0).should('have.value', '10')
	});

	it('import grid from csv', () => {
		cy.import('admin/grids');
		cy.backend('/?initial_path=grids');
		cy.getByTestId('import').selectFile('cypress/fixtures/grid.csv',{
			force: true
		})
		cy.getByTestId('grid').should('exist')
	});
});
