{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "776442f374e7cc5dadb3a78bd8c0d6d3", "packages": [{"name": "api-platform/core", "version": "v2.7.11", "source": {"type": "git", "url": "https://github.com/api-platform/core.git", "reference": "79f548640960c8df36d4048e2de3a7d7113fbd6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/api-platform/core/zipball/79f548640960c8df36d4048e2de3a7d7113fbd6b", "reference": "79f548640960c8df36d4048e2de3a7d7113fbd6b", "shasum": ""}, "require": {"doctrine/inflector": "^1.0 || ^2.0", "php": ">=7.1", "psr/cache": "^1.0 || ^2.0 || ^3.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/http-foundation": "^4.4 || ^5.1 || ^6.0", "symfony/http-kernel": "^4.4 || ^5.1 || ^6.0", "symfony/property-access": "^3.4.19 || ^4.4 || ^5.1 || ^6.0", "symfony/property-info": "^3.4 || ^4.4 || ^5.2.1 || ^6.0", "symfony/serializer": "^4.4 || ^5.1 || ^6.0", "symfony/web-link": "^4.4 || ^5.1 || ^6.0", "willdurand/negotiation": "^2.0.3 || ^3.0"}, "conflict": {"doctrine/common": "<2.7", "doctrine/dbal": "<2.10", "doctrine/mongodb-odm": "<2.2", "doctrine/persistence": "<1.3", "elasticsearch/elasticsearch": ">=8.0"}, "require-dev": {"behat/behat": "^3.1", "behat/mink": "^1.9@dev", "doctrine/annotations": "^1.7", "doctrine/cache": "^1.11 || ^2.1", "doctrine/common": "^2.11 || ^3.0", "doctrine/data-fixtures": "^1.2.2", "doctrine/dbal": "^2.6 || ^3.0", "doctrine/doctrine-bundle": "^1.12 || ^2.0", "doctrine/mongodb-odm": "^2.2", "doctrine/mongodb-odm-bundle": "^4.0", "doctrine/orm": "^2.6.4", "elasticsearch/elasticsearch": "^7.11.0", "friends-of-behat/mink-browserkit-driver": "^1.3.1", "friends-of-behat/mink-extension": "^2.2", "friends-of-behat/symfony-extension": "^2.1", "guzzlehttp/guzzle": "^6.0 || ^7.0", "jangregor/phpstan-prophecy": "^1.0", "justinrainbow/json-schema": "^5.2.1", "phpdocumentor/reflection-docblock": "^3.0 || ^4.0 || ^5.1", "phpdocumentor/type-resolver": "^0.3 || ^0.4 || ^1.4", "phpspec/prophecy": "^1.10", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.1", "phpstan/phpstan-doctrine": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-symfony": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "ramsey/uuid": "^3.7 || ^4.0", "ramsey/uuid-doctrine": "^1.4", "soyuka/contexts": "^3.3.6", "soyuka/stubs-mongodb": "^1.0", "symfony/asset": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/browser-kit": "^4.4 || ^5.1 || ^6.0", "symfony/cache": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/config": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/console": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/css-selector": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/debug": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/dependency-injection": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/doctrine-bridge": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/dom-crawler": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/event-dispatcher": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/expression-language": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/finder": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/form": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/framework-bundle": "^4.4 || ^5.1 || ^6.0", "symfony/http-client": "^4.4 || ^5.1 || ^6.0", "symfony/intl": "^4.4 || ^5.3 || ^6.0", "symfony/maker-bundle": "^1.24", "symfony/mercure-bundle": "*", "symfony/messenger": "^4.4 || ^5.1 || ^6.0", "symfony/phpunit-bridge": "^5.4 || ^6.0", "symfony/routing": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/security-bundle": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/security-core": "^4.4 || ^5.1 || ^6.0", "symfony/twig-bundle": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/validator": "^3.4 || ^4.4 || ^5.1 || ^6.0", "symfony/web-profiler-bundle": "^4.4 || ^5.1 || ^6.0", "symfony/yaml": "^3.4 || ^4.4 || ^5.1 || ^6.0", "twig/twig": "^1.42.3 || ^2.12 || ^3.0", "webonyx/graphql-php": "^14.0"}, "suggest": {"doctrine/mongodb-odm-bundle": "To support MongoDB. Only versions 4.0 and later are supported.", "elasticsearch/elasticsearch": "To support Elasticsearch.", "ocramius/package-versions": "To display the API Platform's version in the debug bar.", "phpdocumentor/reflection-docblock": "To support extracting metadata from PHPDoc.", "psr/cache-implementation": "To use metadata caching.", "ramsey/uuid": "To support <PERSON>'s UUID identifiers.", "symfony/cache": "To have metadata caching when using Symfony integration.", "symfony/config": "To load XML configuration files.", "symfony/expression-language": "To use authorization features.", "symfony/http-client": "To use the HTTP cache invalidation system.", "symfony/messenger": "To support messenger integration.", "symfony/security": "To use authorization features.", "symfony/twig-bundle": "To use the Swagger UI integration.", "symfony/uid": "To support Symfony UUID/ULID identifiers.", "symfony/web-profiler-bundle": "To use the data collector.", "webonyx/graphql-php": "To support GraphQL."}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.7.x-dev"}, "symfony": {"require": "^3.4 || ^4.4 || ^5.1 || ^6.0"}}, "autoload": {"files": ["src/deprecation.php"], "psr-4": {"ApiPlatform\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://dunglas.fr"}], "description": "Build a fully-featured hypermedia or GraphQL API in minutes!", "homepage": "https://api-platform.com", "keywords": ["Hydra", "JSON-LD", "api", "graphql", "hal", "jsonapi", "openapi", "rest", "swagger"], "support": {"issues": "https://github.com/api-platform/core/issues", "source": "https://github.com/api-platform/core/tree/v2.7.11"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/api-platform/core", "type": "tidelift"}], "time": "2023-03-10T09:36:05+00:00"}, {"name": "beberlei/doctrineextensions", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/beberlei/DoctrineExtensions.git", "reference": "008f162f191584a6c37c03a803f718802ba9dd9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beberlei/DoctrineExtensions/zipball/008f162f191584a6c37c03a803f718802ba9dd9a", "reference": "008f162f191584a6c37c03a803f718802ba9dd9a", "shasum": ""}, "require": {"doctrine/orm": "^2.7", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "nesbot/carbon": "*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "symfony/yaml": "^4.2 || ^5.0", "zf1/zend-date": "^1.12", "zf1/zend-registry": "^1.12"}, "type": "library", "autoload": {"psr-4": {"DoctrineExtensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A set of extensions to Doctrine 2 that add support for additional query functions available in MySQL, Oracle, PostgreSQL and SQLite.", "keywords": ["database", "doctrine", "orm"], "support": {"source": "https://github.com/beberlei/DoctrineExtensions/tree/v1.3.0"}, "time": "2020-11-29T07:37:23+00:00"}, {"name": "bjeavons/zxcvbn-php", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/bjeavons/zxcvbn-php.git", "reference": "994928ae5b17ecff8baa2406832d37bdf01116c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bjeavons/zxcvbn-php/zipball/994928ae5b17ecff8baa2406832d37bdf01116c0", "reference": "994928ae5b17ecff8baa2406832d37bdf01116c0", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.2 | ^8.0 | ^8.1", "symfony/polyfill-mbstring": ">=1.3.1"}, "require-dev": {"php-coveralls/php-coveralls": "*", "phpunit/phpunit": "^8.5", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-gmp": "Required for optimized binomial calculations (also requires PHP >= 7.3)"}, "type": "library", "autoload": {"psr-4": {"ZxcvbnPhp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "See contributors", "homepage": "https://github.com/bjeavons/zxcvbn-php"}], "description": "Realistic password strength estimation PHP library based on Zxcvbn JS", "homepage": "https://github.com/bjeavons/zxcvbn-php", "keywords": ["password", "zxcvbn"], "support": {"issues": "https://github.com/bjeavons/zxcvbn-php/issues", "source": "https://github.com/bjeavons/zxcvbn-php/tree/1.3.1"}, "time": "2021-12-21T18:37:02+00:00"}, {"name": "composer/ca-bundle", "version": "1.3.4", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "4c679186f2aca4ab6a0f1b0b9cf9252decb44d0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/4c679186f2aca4ab6a0f1b0b9cf9252decb44d0b", "reference": "4c679186f2aca4ab6a0f1b0b9cf9252decb44d0b", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.3.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-10-28T20:44:15+00:00"}, {"name": "composer/installers", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/d20a64ed3c94748397ff5973488761b22f6d3f19", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "replace": {"roundcube/plugin-installer": "*", "shama/baton": "*"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.3"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["Craft", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "aimeos", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "j<PERSON><PERSON>", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "symfony", "tastyigniter", "typo3", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v1.12.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-09-13T08:19:44+00:00"}, {"name": "cssjanus/cssjanus", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/cssjanus/php-cssjanus.git", "reference": "de7483c0805750a6462b372eab55d022d555df02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cssjanus/php-cssjanus/zipball/de7483c0805750a6462b372eab55d022d555df02", "reference": "de7483c0805750a6462b372eab55d022d555df02", "shasum": ""}, "require": {"php": ">=7.2.0"}, "require-dev": {"mediawiki/mediawiki-phan-config": "0.10.6", "php-parallel-lint/php-parallel-lint": "^1.3.0", "phpunit/phpunit": "^8.5.15", "squizlabs/php_codesniffer": "^3.6.0"}, "type": "library", "autoload": {"psr-0": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "Convert CSS stylesheets between left-to-right and right-to-left.", "support": {"issues": "https://github.com/cssjanus/php-cssjanus/issues", "source": "https://github.com/cssjanus/php-cssjanus/tree/v2.1.0"}, "time": "2021-09-09T17:58:26+00:00"}, {"name": "curl/curl", "version": "2.3.3", "source": {"type": "git", "url": "https://github.com/php-mod/curl.git", "reference": "ec22ad27dead47093f0944f5e651df4b12846f5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-mod/curl/zipball/ec22ad27dead47093f0944f5e651df4b12846f5a", "reference": "ec22ad27dead47093f0944f5e651df4b12846f5a", "shasum": ""}, "require": {"ext-curl": "*", "php": "^5.6 | ^7.0 | ^8.0"}, "require-dev": {"yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "autoload": {"psr-0": {"Curl": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "php-curl-class", "homepage": "https://github.com/php-curl-class"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://hassan.amouhzi.com"}, {"name": "user52", "homepage": "https://github.com/user52"}], "description": "cURL class for PHP", "homepage": "https://github.com/php-mod/curl", "keywords": ["curl", "dot"], "support": {"issues": "https://github.com/php-mod/curl/issues", "source": "https://github.com/php-mod/curl/tree/2.3.3"}, "time": "2021-11-30T20:19:35+00:00"}, {"name": "defuse/php-encryption", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/defuse/php-encryption.git", "reference": "77880488b9954b7884c25555c2a0ea9e7053f9d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/77880488b9954b7884c25555c2a0ea9e7053f9d2", "reference": "77880488b9954b7884c25555c2a0ea9e7053f9d2", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^4|^5|^6|^7|^8|^9"}, "bin": ["bin/generate-defuse-key"], "type": "library", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "support": {"issues": "https://github.com/defuse/php-encryption/issues", "source": "https://github.com/defuse/php-encryption/tree/v2.3.1"}, "time": "2021-04-09T23:57:26+00:00"}, {"name": "doctrine/annotations", "version": "1.14.1", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "9e034d7a70032d422169f27d8759e8d84abb4f51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/9e034d7a70032d422169f27d8759e8d84abb4f51", "reference": "9e034d7a70032d422169f27d8759e8d84abb4f51", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.1"}, "time": "2022-12-12T12:46:12+00:00"}, {"name": "doctrine/cache", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "331b4d5dbaeab3827976273e9356b3b453c300ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/331b4d5dbaeab3827976273e9356b3b453c300ce", "reference": "331b4d5dbaeab3827976273e9356b3b453c300ce", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^8.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.2 || ^6.0@dev", "symfony/var-exporter": "^4.4 || ^5.2 || ^6.0@dev"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2021-07-17T14:49:29+00:00"}, {"name": "doctrine/collections", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0 || ^10.0", "phpstan/phpstan": "^1.4.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.8.0"}, "time": "2022-09-01T20:12:10+00:00"}, {"name": "doctrine/common", "version": "3.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "c824e95d4c83b7102d8bc60595445a6f7d540f96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/c824e95d4c83b7102d8bc60595445a6f7d540f96", "reference": "c824e95d4c83b7102d8bc60595445a6f7d540f96", "shasum": ""}, "require": {"doctrine/persistence": "^2.0 || ^3.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.4.1", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5.20 || ^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5", "vimeo/psalm": "^4.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, proxies and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/3.3.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2022-02-05T18:28:51+00:00"}, {"name": "doctrine/dbal", "version": "2.13.8", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "dc9b3c3c8592c935a6e590441f9abc0f9eba335b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/dc9b3c3c8592c935a6e590441f9abc0f9eba335b", "reference": "dc9b3c3c8592c935a6e590441f9abc0f9eba335b", "shasum": ""}, "require": {"doctrine/cache": "^1.0|^2.0", "doctrine/deprecations": "^0.5.3", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2021.1", "phpstan/phpstan": "1.4.6", "phpunit/phpunit": "^7.5.20|^8.5|9.5.16", "psalm/plugin-phpunit": "0.16.1", "squizlabs/php_codesniffer": "3.6.2", "symfony/cache": "^4.4", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.22.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.13.8"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2022-03-09T15:25:46+00:00"}, {"name": "doctrine/deprecations", "version": "v0.5.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "9504165960a1f83cc1480e2be1dd0a0478561314"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/9504165960a1f83cc1480e2be1dd0a0478561314", "reference": "9504165960a1f83cc1480e2be1dd0a0478561314", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0|^7.0|^8.0", "phpunit/phpunit": "^7.0|^8.0|^9.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v0.5.3"}, "time": "2021-03-21T12:59:47+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "2.6.3", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "527970d22b8ca6472ebd88d7c42e512550bd874e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/527970d22b8ca6472ebd88d7c42e512550bd874e", "reference": "527970d22b8ca6472ebd88d7c42e512550bd874e", "shasum": ""}, "require": {"doctrine/annotations": "^1", "doctrine/cache": "^1.11 || ^2.0", "doctrine/dbal": "^2.13.1|^3.3.2", "doctrine/persistence": "^2.2|^3", "doctrine/sql-formatter": "^1.0.1", "php": "^7.1 || ^8.0", "symfony/cache": "^4.3.3|^5.0|^6.0", "symfony/config": "^4.4.3|^5.0|^6.0", "symfony/console": "^3.4.30|^4.3.3|^5.0|^6.0", "symfony/dependency-injection": "^4.4.18|^5.0|^6.0", "symfony/deprecation-contracts": "^2.1|^3", "symfony/doctrine-bridge": "^4.4.22|^5.2.7|^6.0", "symfony/framework-bundle": "^3.4.30|^4.3.3|^5.0|^6.0", "symfony/service-contracts": "^1.1.1|^2.0|^3"}, "conflict": {"doctrine/orm": "<2.10|>=3.0", "twig/twig": "<1.34|>=2.0,<2.4"}, "require-dev": {"doctrine/coding-standard": "^9.0", "doctrine/orm": "^2.10 || ^3.0", "friendsofphp/proxy-manager-lts": "^1.0", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.3 || ^10.0", "psalm/plugin-phpunit": "^0.16.1", "psalm/plugin-symfony": "^3", "psr/log": "^1.1.4|^2.0|^3.0", "symfony/phpunit-bridge": "^5.2|^6.0", "symfony/property-info": "^4.3.3|^5.0|^6.0", "symfony/proxy-manager-bridge": "^3.4|^4.3.3|^5.0|^6.0", "symfony/security-bundle": "^4.4|^5.0|^6.0", "symfony/twig-bridge": "^3.4.30|^4.3.3|^5.0|^6.0", "symfony/validator": "^3.4.30|^4.3.3|^5.0|^6.0", "symfony/web-profiler-bundle": "^3.4.30|^4.3.3|^5.0|^6.0", "symfony/yaml": "^3.4.30|^4.3.3|^5.0|^6.0", "twig/twig": "^1.34|^2.12|^3.0", "vimeo/psalm": "^4.7"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "ext-pdo": "*", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "https://www.doctrine-project.org/"}], "description": "Symfony DoctrineBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineBundle/issues", "source": "https://github.com/doctrine/DoctrineBundle/tree/2.6.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-bundle", "type": "tidelift"}], "time": "2022-04-22T09:59:53+00:00"}, {"name": "doctrine/event-manager", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:51:15+00:00"}, {"name": "doctrine/inflector", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "ade2b3bbfb776f27f0558e26eed43b5d9fe1b392"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/ade2b3bbfb776f27f0558e26eed43b5d9fe1b392", "reference": "ade2b3bbfb776f27f0558e26eed43b5d9fe1b392", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.5"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2022-09-07T09:01:28+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/10dcfce151b967d20fde1b34ae6640712c3891bc", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-03-03T08:28:38+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "doctrine/orm", "version": "2.12.1", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "2e4a8722721b934149ff53b191522a6829b6d73b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/2e4a8722721b934149ff53b191522a6829b6d73b", "reference": "2e4a8722721b934149ff53b191522a6829b6d73b", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.12.1 || ^2.1.1", "doctrine/collections": "^1.5", "doctrine/common": "^3.0.3", "doctrine/dbal": "^2.13.1 || ^3.2", "doctrine/deprecations": "^0.5.3", "doctrine/event-manager": "^1.1", "doctrine/inflector": "^1.4 || ^2.0", "doctrine/instantiator": "^1.3", "doctrine/lexer": "^1.2.3", "doctrine/persistence": "^2.4 || ^3", "ext-ctype": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3", "symfony/console": "^3.0 || ^4.0 || ^5.0 || ^6.0", "symfony/polyfill-php72": "^1.23", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/annotations": "<1.13 || >= 2.0"}, "require-dev": {"doctrine/annotations": "^1.13", "doctrine/coding-standard": "^9.0", "phpbench/phpbench": "^0.16.10 || ^1.0", "phpstan/phpstan": "~1.4.10 || 1.5.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.4", "psr/log": "^1 || ^2 || ^3", "squizlabs/php_codesniffer": "3.6.2", "symfony/cache": "^4.4 || ^5.4 || ^6.0", "symfony/yaml": "^3.4 || ^4.0 || ^5.0 || ^6.0", "vimeo/psalm": "4.22.0"}, "suggest": {"symfony/cache": "Provides cache support for Setup Tool with doctrine/cache 2.0", "symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine"], "type": "library", "autoload": {"psr-4": {"Doctrine\\ORM\\": "lib/Doctrine/ORM"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/doctrine/orm/issues", "source": "https://github.com/doctrine/orm/tree/2.12.1"}, "time": "2022-04-22T17:46:03+00:00"}, {"name": "doctrine/persistence", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "d87426f07dd66f97cfdcf5210925e483b6c993b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/d87426f07dd66f97cfdcf5210925e483b6c993b5", "reference": "d87426f07dd66f97cfdcf5210925e483b6c993b5", "shasum": ""}, "require": {"doctrine/event-manager": "^1 || ^2", "php": "^7.2 || ^8.0", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "conflict": {"doctrine/annotations": "<1.7 || >=2.0", "doctrine/common": "<2.10"}, "require-dev": {"composer/package-versions-deprecated": "^1.11", "doctrine/annotations": "^1.7", "doctrine/coding-standard": "^10", "doctrine/common": "^3.0", "phpstan/phpstan": "1.8.8", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0", "vimeo/psalm": "4.29.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Persistence\\": "src/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://www.doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/3.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2022-12-13T21:21:05+00:00"}, {"name": "doctrine/sql-formatter", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/doctrine/sql-formatter.git", "reference": "20c39c2de286a9d3262cc8ed282a4ae60e265894"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/sql-formatter/zipball/20c39c2de286a9d3262cc8ed282a4ae60e265894", "reference": "20c39c2de286a9d3262cc8ed282a4ae60e265894", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4"}, "bin": ["bin/sql-formatter"], "type": "library", "autoload": {"psr-4": {"Doctrine\\SqlFormatter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/doctrine/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/doctrine/sql-formatter/issues", "source": "https://github.com/doctrine/sql-formatter/tree/1.1.2"}, "time": "2021-11-05T11:11:14+00:00"}, {"name": "egulias/email-validator", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ee0db30118f661fb166bcffbf5d82032df484697"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ee0db30118f661fb166bcffbf5d82032df484697", "reference": "ee0db30118f661fb166bcffbf5d82032df484697", "shasum": ""}, "require": {"doctrine/lexer": "^1.2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.1.2"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2021-10-11T09:18:27+00:00"}, {"name": "ezyang/htmlpurifier", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "1dd3e52365c32a142fb7c9c9f8f038f18e353270"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/1dd3e52365c32a142fb7c9c9f8f038f18e353270", "reference": "1dd3e52365c32a142fb7c9c9f8f038f18e353270", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "default-branch": true, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/master"}, "time": "2022-04-08T17:48:12+00:00"}, {"name": "friendsofphp/proxy-manager-lts", "version": "v1.0.13", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/proxy-manager-lts.git", "reference": "88354616f4cf4f6620910fd035e282173ba453e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/proxy-manager-lts/zipball/88354616f4cf4f6620910fd035e282173ba453e8", "reference": "88354616f4cf4f6620910fd035e282173ba453e8", "shasum": ""}, "require": {"laminas/laminas-code": "~3.4.1|^4.0", "php": ">=7.1", "symfony/filesystem": "^4.4.17|^5.0|^6.0"}, "conflict": {"laminas/laminas-stdlib": "<3.2.1", "zendframework/zend-stdlib": "<3.2.1"}, "replace": {"ocramius/proxy-manager": "^2.1"}, "require-dev": {"ext-phar": "*", "symfony/phpunit-bridge": "^5.4|^6.0"}, "type": "library", "extra": {"thanks": {"name": "ocramius/proxy-manager", "url": "https://github.com/Ocramius/ProxyManager"}}, "autoload": {"psr-4": {"ProxyManager\\": "src/ProxyManager"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adding support for a wider range of PHP versions to ocramius/proxy-manager", "homepage": "https://github.com/FriendsOfPHP/proxy-manager-lts", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "support": {"issues": "https://github.com/FriendsOfPHP/proxy-manager-lts/issues", "source": "https://github.com/FriendsOfPHP/proxy-manager-lts/tree/v1.0.13"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2022-10-17T19:48:16+00:00"}, {"name": "friendsofsymfony/jsrouting-bundle", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle.git", "reference": "c978fabc6a21a77052ff3fe927b41111ec944f0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSJsRoutingBundle/zipball/c978fabc6a21a77052ff3fe927b41111ec944f0d", "reference": "c978fabc6a21a77052ff3fe927b41111ec944f0d", "shasum": ""}, "require": {"php": "^7.1|^8.0", "symfony/console": "~3.4|^4.4.20|^5.0", "symfony/framework-bundle": "~3.4|^4.4.20|^5.0", "symfony/serializer": "~3.4|^4.4.20|^5.0", "willdurand/jsonp-callback-validator": "~1.1"}, "require-dev": {"symfony/expression-language": "~3.4|^4.4.20|^5.0", "symfony/phpunit-bridge": "^5.3"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"FOS\\JsRoutingBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSJsRoutingBundle/contributors"}], "description": "A pretty nice way to expose your Symfony2 routing to client applications.", "homepage": "http://friendsofsymfony.github.com", "keywords": ["Js Routing", "javascript", "routing"], "support": {"issues": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle/issues", "source": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle/tree/2.8.0"}, "time": "2021-12-15T08:51:04+00:00"}, {"name": "geoip2/geoip2", "version": "v2.4.5", "source": {"type": "git", "url": "https://github.com/maxmind/GeoIP2-php.git", "reference": "b28a0ed0190cd76c878ed7002a5d1bb8c5f4c175"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/GeoIP2-php/zipball/b28a0ed0190cd76c878ed7002a5d1bb8c5f4c175", "reference": "b28a0ed0190cd76c878ed7002a5d1bb8c5f4c175", "shasum": ""}, "require": {"maxmind-db/reader": "~1.0", "maxmind/web-service-common": "~0.3", "php": ">=5.3.1"}, "require-dev": {"phpunit/phpunit": "4.2.*", "squizlabs/php_codesniffer": "2.*"}, "type": "library", "autoload": {"psr-4": {"GeoIp2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.maxmind.com/"}], "description": "MaxMind GeoIP2 PHP API", "homepage": "https://github.com/maxmind/GeoIP2-php", "keywords": ["IP", "geoip", "geoip2", "geolocation", "maxmind"], "support": {"issues": "https://github.com/maxmind/GeoIP2-php/issues", "source": "https://github.com/maxmind/GeoIP2-php/tree/master"}, "time": "2017-01-31T17:28:48+00:00"}, {"name": "greenlion/php-sql-parser", "version": "v4.5.0", "source": {"type": "git", "url": "https://github.com/greenlion/PHP-SQL-Parser.git", "reference": "a5d5c292d97271c95140192e6f0e962916e39b50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/greenlion/PHP-SQL-Parser/zipball/a5d5c292d97271c95140192e6f0e962916e39b50", "reference": "a5d5c292d97271c95140192e6f0e962916e39b50", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"analog/analog": "^1.0.6", "phpunit/phpunit": "^9.5.13", "squizlabs/php_codesniffer": "^1.5.1"}, "type": "library", "autoload": {"psr-0": {"PHPSQLParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://code.google.com/u/<EMAIL>/", "role": "Owner"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.phosco.info", "role": "Committer"}], "description": "A pure PHP SQL (non validating) parser w/ focus on MySQL dialect of SQL", "homepage": "https://github.com/greenlion/PHP-SQL-Parser", "keywords": ["creator", "mysql", "parser", "sql"], "support": {"issues": "https://github.com/greenlion/PHP-SQL-Parser/issues", "source": "https://github.com/greenlion/PHP-SQL-Parser"}, "time": "2022-02-01T09:26:56+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.5.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "b50a2a1251152e43f6a37f0fa053e730a67d25ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/b50a2a1251152e43f6a37f0fa053e730a67d25ba", "reference": "b50a2a1251152e43f6a37f0fa053e730a67d25ba", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.9 || ^2.4", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.29 || ^9.5.23", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.5.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-08-28T15:39:27+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "b94b2807d85443f9719887892882d0329d1e2598"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/b94b2807d85443f9719887892882d0329d1e2598", "reference": "b94b2807d85443f9719887892882d0329d1e2598", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2022-08-28T14:55:35+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.4.5", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "0454e12ef0cd597ccd2adb036f7bda4e7fface66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/0454e12ef0cd597ccd2adb036f7bda4e7fface66", "reference": "0454e12ef0cd597ccd2adb036f7bda4e7fface66", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.4.5"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-04-17T16:00:45+00:00"}, {"name": "incenteev/composer-parameter-handler", "version": "v2.1.5", "source": {"type": "git", "url": "https://github.com/Incenteev/ParameterHandler.git", "reference": "084befb11ec21faeadcddefb88b66132775ff59b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Incenteev/ParameterHandler/zipball/084befb11ec21faeadcddefb88b66132775ff59b", "reference": "084befb11ec21faeadcddefb88b66132775ff59b", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/yaml": "^2.3 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"composer/composer": "^1.0@dev", "symfony/filesystem": "^2.3 || ^3 || ^4 || ^5", "symfony/phpunit-bridge": "^4.0 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Incenteev\\ParameterHandler\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Composer script handling your ignored parameter file", "homepage": "https://github.com/Incenteev/ParameterHandler", "keywords": ["parameters management"], "support": {"issues": "https://github.com/Incenteev/ParameterHandler/issues", "source": "https://github.com/Incenteev/ParameterHandler/tree/v2.1.4"}, "time": "2020-03-17T21:10:00+00:00"}, {"name": "intervention/httpauth", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/Intervention/httpauth.git", "reference": "825202e88c0918f5249bd5af6ff1fb8ef6e3271e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/httpauth/zipball/825202e88c0918f5249bd5af6ff1fb8ef6e3271e", "reference": "825202e88c0918f5249bd5af6ff1fb8ef6e3271e", "shasum": ""}, "require": {"php": "^7.2"}, "require-dev": {"phpstan/phpstan": "^0.12.11", "phpunit/phpunit": "^8.0"}, "type": "library", "extra": {"laravel": {"providers": ["Intervention\\HttpAuth\\Laravel\\HttpAuthServiceProvider"], "aliases": {"HttpAuth": "Intervention\\HttpAuth\\Laravel\\Facades\\HttpAuth"}}}, "autoload": {"psr-4": {"Intervention\\HttpAuth\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://olivervogel.com/"}], "description": "HTTP authentication (Basic & Digest) including ServiceProviders for easy Laravel integration", "homepage": "https://github.com/Intervention/httpauth", "keywords": ["Authentication", "http", "laravel"], "support": {"issues": "https://github.com/Intervention/httpauth/issues", "source": "https://github.com/Intervention/httpauth/tree/3.0.1"}, "time": "2020-03-09T16:18:28+00:00"}, {"name": "ircmaxell/password-compat", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/ircmaxell/password_compat.git", "reference": "5c5cde8822a69545767f7c7f3058cb15ff84614c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ircmaxell/password_compat/zipball/5c5cde8822a69545767f7c7f3058cb15ff84614c", "reference": "5c5cde8822a69545767f7c7f3058cb15ff84614c", "shasum": ""}, "require-dev": {"phpunit/phpunit": "4.*"}, "type": "library", "autoload": {"files": ["lib/password.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://blog.ircmaxell.com"}], "description": "A compatibility library for the proposed simplified password hashing algorithm: https://wiki.php.net/rfc/password_hash", "homepage": "https://github.com/ircmaxell/password_compat", "keywords": ["hashing", "password"], "support": {"issues": "https://github.com/ircmaxell/password_compat/issues", "source": "https://github.com/ircmaxell/password_compat/tree/v1.0"}, "time": "2014-11-20T16:49:30+00:00"}, {"name": "ircmaxell/random-lib", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/ircmaxell/RandomLib.git", "reference": "e9e0204f40e49fa4419946c677eccd3fa25b8cf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ircmaxell/RandomLib/zipball/e9e0204f40e49fa4419946c677eccd3fa25b8cf4", "reference": "e9e0204f40e49fa4419946c677eccd3fa25b8cf4", "shasum": ""}, "require": {"ircmaxell/security-lib": "^1.1", "php": ">=5.3.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.11", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^4.8|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-0": {"RandomLib": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://blog.ircmaxell.com"}], "description": "A Library For Generating Secure Random Numbers", "homepage": "https://github.com/ircmaxell/RandomLib", "keywords": ["cryptography", "random", "random-numbers", "random-strings"], "support": {"issues": "https://github.com/ircmaxell/RandomLib/issues", "source": "https://github.com/ircmaxell/RandomLib/tree/master"}, "time": "2016-09-07T15:52:06+00:00"}, {"name": "ircmaxell/security-lib", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/ircmaxell/SecurityLib.git", "reference": "f3db6de12c20c9bcd1aa3db4353a1bbe0e44e1b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ircmaxell/SecurityLib/zipball/f3db6de12c20c9bcd1aa3db4353a1bbe0e44e1b5", "reference": "f3db6de12c20c9bcd1aa3db4353a1bbe0e44e1b5", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"mikey179/vfsstream": "1.1.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"SecurityLib": "lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://blog.ircmaxell.com"}], "description": "A Base Security Library", "homepage": "https://github.com/ircmaxell/SecurityLib", "support": {"issues": "https://github.com/ircmaxell/SecurityLib/issues", "source": "https://github.com/ircmaxell/SecurityLib/tree/master"}, "time": "2015-03-20T14:31:23+00:00"}, {"name": "jakeasmith/http_build_url", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/jakeasmith/http_build_url.git", "reference": "93c273e77cb1edead0cf8bcf8cd2003428e74e37"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jakeasmith/http_build_url/zipball/93c273e77cb1edead0cf8bcf8cd2003428e74e37", "reference": "93c273e77cb1edead0cf8bcf8cd2003428e74e37", "shasum": ""}, "type": "library", "autoload": {"files": ["src/http_build_url.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality for http_build_url() to environments without pecl_http.", "support": {"issues": "https://github.com/jakeasmith/http_build_url/issues", "source": "https://github.com/jakeasmith/http_build_url"}, "time": "2017-05-01T15:36:40+00:00"}, {"name": "lcobucci/jwt", "version": "dev-3.4.6-patch", "source": {"type": "git", "url": "https://github.com/PrestaShop/jwt.git", "reference": "da86a58c07f37a2fb59e648725910cc82d2a4975"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/jwt/zipball/da86a58c07f37a2fb59e648725910cc82d2a4975", "reference": "da86a58c07f37a2fb59e648725910cc82d2a4975", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-openssl": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"mikey179/vfsstream": "~1.5", "phpmd/phpmd": "~2.2", "phpunit/php-invoker": "~1.1", "phpunit/phpunit": "^8.5", "squizlabs/php_codesniffer": "~2.3"}, "suggest": {"lcobucci/clock": "*"}, "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}, "files": ["compat/class-aliases.php", "compat/json-exception-polyfill.php", "compat/lcobucci-clock-polyfill.php"]}, "autoload-dev": {"psr-4": {"Lcobucci\\JWT\\": ["test/unit", "test/functional"]}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "JWT"], "support": {"source": "https://github.com/PrestaShop/jwt/tree/3.4.6-patch"}, "time": "2022-10-07T13:56:08+00:00"}, {"name": "league/event", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/event.git", "reference": "d2cc124cf9a3fab2bb4ff963307f60361ce4d119"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/event/zipball/d2cc124cf9a3fab2bb4ff963307f60361ce4d119", "reference": "d2cc124cf9a3fab2bb4ff963307f60361ce4d119", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "~1.0.1", "phpspec/phpspec": "^2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"League\\Event\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Event package", "keywords": ["emitter", "event", "listener"], "support": {"issues": "https://github.com/thephpleague/event/issues", "source": "https://github.com/thephpleague/event/tree/master"}, "time": "2018-11-26T11:52:41+00:00"}, {"name": "league/oauth2-server", "version": "8.3.5", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-server.git", "reference": "7aeb7c42b463b1a6fe4d084d3145e2fa22436876"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-server/zipball/7aeb7c42b463b1a6fe4d084d3145e2fa22436876", "reference": "7aeb7c42b463b1a6fe4d084d3145e2fa22436876", "shasum": ""}, "require": {"defuse/php-encryption": "^2.2.1", "ext-json": "*", "ext-openssl": "*", "lcobucci/jwt": "^3.4.6 || ^4.0.4", "league/event": "^2.2", "league/uri": "^6.4", "php": "^7.2 || ^8.0", "psr/http-message": "^1.0.1"}, "replace": {"league/oauth2server": "*", "lncd/oauth2": "*"}, "require-dev": {"laminas/laminas-diactoros": "^2.4.1", "phpstan/phpstan": "^0.12.57", "phpstan/phpstan-phpunit": "^0.12.16", "phpunit/phpunit": "^8.5.13", "roave/security-advisories": "dev-master"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.noexceptions.io", "role": "Developer"}], "description": "A lightweight and powerful OAuth 2.0 authorization and resource server library with support for all the core specification grants. This library will allow you to secure your API with OAuth and allow your applications users to approve apps that want to access their data from your API.", "homepage": "https://oauth2.thephpleague.com/", "keywords": ["Authentication", "api", "auth", "authorisation", "authorization", "o<PERSON>h", "oauth 2", "oauth 2.0", "oauth2", "protect", "resource", "secure", "server"], "support": {"issues": "https://github.com/thephpleague/oauth2-server/issues", "source": "https://github.com/thephpleague/oauth2-server/tree/8.3.5"}, "funding": [{"url": "https://github.com/sephster", "type": "github"}], "time": "2022-05-03T21:21:28+00:00"}, {"name": "league/tactician", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/thephpleague/tactician.git", "reference": "e79f763170f3d5922ec29e85cffca0bac5cd8975"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/tactician/zipball/e79f763170f3d5922ec29e85cffca0bac5cd8975", "reference": "e79f763170f3d5922ec29e85cffca0bac5cd8975", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5.20 || ^9.3.8", "squizlabs/php_codesniffer": "^3.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"League\\Tactician\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://tactician.thephpleague.com"}], "description": "A small, flexible command bus. Handy for building service layers.", "keywords": ["command", "command bus", "service layer"], "support": {"issues": "https://github.com/thephpleague/tactician/issues", "source": "https://github.com/thephpleague/tactician/tree/v1.1.0"}, "time": "2021-02-14T15:29:04+00:00"}, {"name": "league/tactician-bundle", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/thephpleague/tactician-bundle.git", "reference": "8340e83ded7c804d2b2c417a0f697fac9c15e520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/tactician-bundle/zipball/8340e83ded7c804d2b2c417a0f697fac9c15e520", "reference": "8340e83ded7c804d2b2c417a0f697fac9c15e520", "shasum": ""}, "require": {"league/tactician": "^1.0", "league/tactician-container": "^2.0|^3.0", "league/tactician-logger": "^0.10|^0.11", "php": ">=7.2", "symfony/config": "^3.4|^4.4|^5.0|^6.0", "symfony/dependency-injection": "^3.4|^4.4|^5.0|^6.0", "symfony/http-kernel": "^3.4|^4.4|^5.0|^6.0", "symfony/yaml": "^3.4|^4.4|^5.0|^6.0"}, "require-dev": {"matthiasnoback/symfony-config-test": "^4.2.1", "matthiasnoback/symfony-dependency-injection-test": "^4.2.1", "mockery/mockery": "~1.0", "phpunit/phpunit": "~8.5", "symfony/console": "^3.4|^4.4|^5.0|^6.0", "symfony/framework-bundle": "^3.4.31|^4.4|^5.0|^6.0", "symfony/security-bundle": "^3.4|^4.4|^5.0|^6.0", "symfony/security-core": "^3.4|^4.4|^5.0|^6.0", "symfony/validator": "^3.4|^4.4|^5.0|^6.0"}, "suggest": {"league/tactician-doctrine": "For doctrine transaction middleware", "symfony/console": "For debugging command-to-handler routing using the tactician:debug console command", "symfony/security": "For command security middleware", "symfony/validator": "For command validator middleware"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"League\\Tactician\\Bundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://doh.ms"}, {"name": "<PERSON>", "homepage": "http://www.rtuin.nl/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Bundle to integrate Tactician with Symfony projects", "keywords": ["bundle", "symfony", "tactician"], "support": {"issues": "https://github.com/thephpleague/tactician-bundle/issues", "source": "https://github.com/thephpleague/tactician-bundle/tree/v1.4.0"}, "time": "2022-06-15T06:56:49+00:00"}, {"name": "league/tactician-container", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/thephpleague/tactician-container.git", "reference": "d1a5d884e072b8cafbff802d07766076eb2ffcb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/tactician-container/zipball/d1a5d884e072b8cafbff802d07766076eb2ffcb0", "reference": "d1a5d884e072b8cafbff802d07766076eb2ffcb0", "shasum": ""}, "require": {"league/tactician": "^1.0", "php": ">=5.5", "psr/container": "^1.0"}, "require-dev": {"league/container": "~2.3", "phpunit/phpunit": "~4.3", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "autoload": {"psr-4": {"League\\Tactician\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://futurepixels.co.uk"}], "description": "Tactician integration for any container implementing PSR-11", "keywords": ["container", "container-interop", "di", "interoperable", "league", "tactician"], "support": {"issues": "https://github.com/thephpleague/tactician-container/issues", "source": "https://github.com/thephpleague/tactician-container/tree/master"}, "time": "2017-04-13T06:27:12+00:00"}, {"name": "league/tactician-logger", "version": "v0.10.0", "source": {"type": "git", "url": "https://github.com/thephpleague/tactician-logger.git", "reference": "3ff9ee04e4cbec100af827f829ed4c7ff7c08442"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/tactician-logger/zipball/3ff9ee04e4cbec100af827f829ed4c7ff7c08442", "reference": "3ff9ee04e4cbec100af827f829ed4c7ff7c08442", "shasum": ""}, "require": {"league/tactician": "^1.0", "php": ">=5.5.0", "psr/log": "~1.0"}, "require-dev": {"mockery/mockery": "^0.9", "phpunit/phpunit": "4.*", "squizlabs/php_codesniffer": "~2.3"}, "type": "library", "autoload": {"psr-4": {"League\\Tactician\\Logger\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Adds PSR-3 logging support to the Tactician command bus", "homepage": "https://github.com/thephpleague/tactician-logger", "keywords": ["log", "logging", "tactician"], "support": {"issues": "https://github.com/thephpleague/tactician-logger/issues", "source": "https://github.com/thephpleague/tactician-logger/tree/master"}, "time": "2016-08-23T05:50:38+00:00"}, {"name": "league/uri", "version": "6.4.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri.git", "reference": "09da64118eaf4c5d52f9923a1e6a5be1da52fd9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri/zipball/09da64118eaf4c5d52f9923a1e6a5be1da52fd9a", "reference": "09da64118eaf4c5d52f9923a1e6a5be1da52fd9a", "shasum": ""}, "require": {"ext-json": "*", "league/uri-interfaces": "^2.1", "php": ">=7.2", "psr/http-message": "^1.0"}, "conflict": {"league/uri-schemes": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^8.0 || ^9.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-fileinfo": "Needed to create Data URI from a filepath", "ext-intl": "Needed to improve host validation", "league/uri-components": "Needed to easily manipulate URI objects", "psr/http-factory": "Needed to use the URI factory"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "http://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "uri-template", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.4.0"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2020-11-22T14:29:11+00:00"}, {"name": "league/uri-interfaces", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-interfaces.git", "reference": "00e7e2943f76d8cb50c7dfdc2f6dee356e15e383"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/00e7e2943f76d8cb50c7dfdc2f6dee356e15e383", "reference": "00e7e2943f76d8cb50c7dfdc2f6dee356e15e383", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19", "phpstan/phpstan": "^0.12.90", "phpstan/phpstan-phpunit": "^0.12.19", "phpstan/phpstan-strict-rules": "^0.12.9", "phpunit/phpunit": "^8.5.15 || ^9.5"}, "suggest": {"ext-intl": "to use the IDNA feature", "symfony/intl": "to use the IDNA feature via Symfony Polyfill"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interface for URI representation", "homepage": "http://github.com/thephpleague/uri-interfaces", "keywords": ["rfc3986", "rfc3987", "uri", "url"], "support": {"issues": "https://github.com/thephpleague/uri-interfaces/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/2.3.0"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2021-06-28T04:27:21+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/2.1.0"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}, {"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2020-05-30T13:11:16+00:00"}, {"name": "marcusschwarz/lesserphp", "version": "v0.5.5", "source": {"type": "git", "url": "https://github.com/MarcusSchwarz/lesserphp.git", "reference": "77ba82b5218ff228267d3b0e5ec8697be75e86a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarcusSchwarz/lesserphp/zipball/77ba82b5218ff228267d3b0e5ec8697be75e86a7", "reference": "77ba82b5218ff228267d3b0e5ec8697be75e86a7", "shasum": ""}, "require-dev": {"phpunit/phpunit": ">=4.8.35 <8"}, "bin": ["plessc"], "type": "library", "extra": {"branch-alias": {"dev-master": "0.5.1-dev"}}, "autoload": {"classmap": ["lessc.inc.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT", "GPL-3.0"], "authors": [{"name": "Leaf Corcoran", "email": "<EMAIL>", "homepage": "http://leafo.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maswaba.de"}], "description": "lesserphp is a compiler for LESS written in PHP based on leaf<PERSON>'s lessphp.", "homepage": "http://leafo.net/lessphp/", "support": {"issues": "https://github.com/MarcusSchwarz/lesserphp/issues", "source": "https://github.com/MarcusSchwarz/lesserphp/tree/v0.5.5"}, "time": "2021-03-10T17:56:57+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "martin<PERSON>he/php-mb-helpers", "version": "0.1.7", "source": {"type": "git", "url": "https://github.com/martinlindhe/php-mb-helpers.git", "reference": "d12570aff7f44f17c1e8b2da59795fe847a7a7c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/martinlindhe/php-mb-helpers/zipball/d12570aff7f44f17c1e8b2da59795fe847a7a7c6", "reference": "d12570aff7f44f17c1e8b2da59795fe847a7a7c6", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.6"}, "type": "library", "autoload": {"files": ["src/mb_helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides mb_ucwords(), mb_ucfirst(), mb_strrev(), mb_str_pad(), mb_count_chars(), mb_str_split()", "homepage": "https://github.com/martinlindhe/php-mb-helpers", "support": {"issues": "https://github.com/martinlindhe/php-mb-helpers/issues", "source": "https://github.com/martinlindhe/php-mb-helpers/tree/0.1.7"}, "time": "2021-03-17T22:34:41+00:00"}, {"name": "matthi<PERSON><PERSON><PERSON>/minify", "version": "1.3.69", "source": {"type": "git", "url": "https://github.com/matthiasmullie/minify.git", "reference": "c00fb02f71b2ef0a5f53fe18c5a8b9aa30f48297"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasmullie/minify/zipball/c00fb02f71b2ef0a5f53fe18c5a8b9aa30f48297", "reference": "c00fb02f71b2ef0a5f53fe18c5a8b9aa30f48297", "shasum": ""}, "require": {"ext-pcre": "*", "matthiasmullie/path-converter": "~1.1", "php": ">=5.3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.0", "matthiasmullie/scrapbook": "dev-master", "phpunit/phpunit": ">=4.8"}, "suggest": {"psr/cache-implementation": "Cache implementation to use with Minify::cache"}, "bin": ["bin/minifycss", "bin/minifyjs"], "type": "library", "autoload": {"psr-4": {"MatthiasMullie\\Minify\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.mullie.eu", "role": "Developer"}], "description": "CSS & JavaScript minifier, in PHP. Removes whitespace, strips comments, combines files (incl. @import statements and small assets in CSS files), and optimizes/shortens a few common programming patterns.", "homepage": "http://www.minifier.org", "keywords": ["JS", "css", "javascript", "minifier", "minify"], "support": {"issues": "https://github.com/matthiasmullie/minify/issues", "source": "https://github.com/matthiasmullie/minify/tree/1.3.68"}, "funding": [{"url": "https://github.com/matthiasmullie", "type": "github"}], "time": "2022-04-19T08:28:56+00:00"}, {"name": "mat<PERSON><PERSON><PERSON><PERSON>/path-converter", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/matthiasmullie/path-converter.git", "reference": "e7d13b2c7e2f2268e1424aaed02085518afa02d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasmullie/path-converter/zipball/e7d13b2c7e2f2268e1424aaed02085518afa02d9", "reference": "e7d13b2c7e2f2268e1424aaed02085518afa02d9", "shasum": ""}, "require": {"ext-pcre": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-4": {"MatthiasMullie\\PathConverter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.mullie.eu", "role": "Developer"}], "description": "Relative path converter", "homepage": "http://github.com/matthiasmullie/path-converter", "keywords": ["converter", "path", "paths", "relative"], "support": {"issues": "https://github.com/matthiasmullie/path-converter/issues", "source": "https://github.com/matthiasmullie/path-converter/tree/1.1.3"}, "time": "2019-02-05T23:41:09+00:00"}, {"name": "maxmind-db/reader", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/maxmind/MaxMind-DB-Reader-php.git", "reference": "b1f3c0699525336d09cc5161a2861268d9f2ae5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/MaxMind-DB-Reader-php/zipball/b1f3c0699525336d09cc5161a2861268d9f2ae5b", "reference": "b1f3c0699525336d09cc5161a2861268d9f2ae5b", "shasum": ""}, "require": {"php": ">=7.2"}, "conflict": {"ext-maxminddb": "<1.10.1,>=2.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "php-coveralls/php-coveralls": "^2.1", "phpstan/phpstan": "*", "phpunit/phpcov": ">=6.0.0", "phpunit/phpunit": ">=8.0.0,<10.0.0", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-bcmath": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-gmp": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-maxminddb": "A C-based database decoder that provides significantly faster lookups"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Db\\": "src/MaxMind/Db"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind DB Reader API", "homepage": "https://github.com/maxmind/MaxMind-DB-Reader-php", "keywords": ["database", "geoip", "geoip2", "geolocation", "maxmind"], "support": {"issues": "https://github.com/maxmind/MaxMind-DB-Reader-php/issues", "source": "https://github.com/maxmind/MaxMind-DB-Reader-php/tree/v1.11.0"}, "time": "2021-10-18T15:23:10+00:00"}, {"name": "maxmind/web-service-common", "version": "v0.9.0", "source": {"type": "git", "url": "https://github.com/maxmind/web-service-common-php.git", "reference": "4dc5a3e8df38aea4ca3b1096cee3a038094e9b53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/web-service-common-php/zipball/4dc5a3e8df38aea4ca3b1096cee3a038094e9b53", "reference": "4dc5a3e8df38aea4ca3b1096cee3a038094e9b53", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0.3", "ext-curl": "*", "ext-json": "*", "php": ">=7.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "phpstan/phpstan": "*", "phpunit/phpunit": "^8.0 || ^9.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Exception\\": "src/Exception", "MaxMind\\WebService\\": "src/WebService"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Internal MaxMind Web Service API", "homepage": "https://github.com/maxmind/web-service-common-php", "support": {"issues": "https://github.com/maxmind/web-service-common-php/issues", "source": "https://github.com/maxmind/web-service-common-php/tree/v0.9.0"}, "time": "2022-03-28T17:43:20+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.41", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "0fd6753003fc870f6e229bae869cc1337c99bc45"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/0fd6753003fc870f6e229bae869cc1337c99bc45", "reference": "0fd6753003fc870f6e229bae869cc1337c99bc45", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.35||~5.7"}, "type": "library", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.39"}, "time": "2022-02-17T19:24:25+00:00"}, {"name": "monolog/monolog", "version": "1.27.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-06-09T08:53:42+00:00"}, {"name": "mrclay/jsmin-php", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/mrclay/jsmin-php.git", "reference": "208e4122f8a273314e81c6b3bee897b5f3c1dc70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mrclay/jsmin-php/zipball/208e4122f8a273314e81c6b3bee897b5f3c1dc70", "reference": "208e4122f8a273314e81c6b3bee897b5f3c1dc70", "shasum": ""}, "require": {"ext-pcre": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.2"}, "type": "library", "autoload": {"psr-0": {"JSMin\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Provides a modified port of <PERSON>'s jsmin.c, which removes unnecessary whitespace from JavaScript files.", "homepage": "https://github.com/mrclay/jsmin-php/", "keywords": ["compress", "jsmin", "minify"], "support": {"email": "<EMAIL>", "issues": "https://github.com/mrclay/jsmin-php/issues", "source": "https://github.com/mrclay/jsmin-php/tree/2.4.1"}, "time": "2022-03-26T14:41:59+00:00"}, {"name": "mrclay/minify", "version": "3.0.11", "source": {"type": "git", "url": "https://github.com/mrclay/minify.git", "reference": "f1572a580a8ab29c5c7df4319c4787c74f7dcb3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mrclay/minify/zipball/f1572a580a8ab29c5c7df4319c4787c74f7dcb3e", "reference": "f1572a580a8ab29c5c7df4319c4787c74f7dcb3e", "shasum": ""}, "require": {"ext-pcre": "*", "intervention/httpauth": "^2.0|^3.0", "marcusschwarz/lesserphp": "^0.5.1", "monolog/monolog": "~1.1|~2.0", "mrclay/jsmin-php": "~2", "mrclay/props-dic": "^2.2|^3.0", "php": "^5.3.0 || ^7.0 || ^8.0", "tubalmartin/cssmin": "~4"}, "require-dev": {"firephp/firephp-core": "~0.4.0", "leafo/scssphp": "^0.3 || ^0.6 || ^0.7", "meenie/javascript-packer": "~1.1", "phpunit/phpunit": "^4.8.36", "tedivm/jshrink": "~1.1.0"}, "suggest": {"firephp/firephp-core": "Use FirePHP for Log messages", "meenie/javascript-packer": "Keep track of the Packer PHP port using Composer"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Minify is a PHP app that helps you follow several rules for client-side performance. It combines multiple CSS or Javascript files, removes unnecessary whitespace and comments, and serves them with gzip encoding and optimal client-side cache headers", "homepage": "https://github.com/mrclay/minify", "support": {"email": "<EMAIL>", "issues": "https://github.com/mrclay/minify/issues", "source": "https://github.com/mrclay/minify/tree/3.0.11", "wiki": "https://github.com/mrclay/minify/blob/master/docs"}, "time": "2021-03-11T11:58:14+00:00"}, {"name": "mrclay/props-dic", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/mrclay/Props.git", "reference": "0b0fd254e33e2d60bc2bcd7867f2ab3cdd05a843"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mrclay/Props/zipball/0b0fd254e33e2d60bc2bcd7867f2ab3cdd05a843", "reference": "0b0fd254e33e2d60bc2bcd7867f2ab3cdd05a843", "shasum": ""}, "require": {"php": ">=5.3.3", "pimple/pimple": "~3.0", "psr/container": "^1.0"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-0": {"Props\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.mrclay.org/"}], "description": "Props is a simple DI container that allows retrieving values via custom property and method names", "keywords": ["container", "dependency injection", "dependency injection container", "di", "di container"], "support": {"issues": "https://github.com/mrclay/Props/issues", "source": "https://github.com/mrclay/Props/tree/master"}, "time": "2019-11-26T17:56:10+00:00"}, {"name": "myclabs/php-enum", "version": "1.7.7", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/d178027d1e679832db9f38248fcc7200647dc2b7", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.7"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2020-11-14T18:14:52+00:00"}, {"name": "nikic/php-parser", "version": "v4.15.2", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "210577fe3cf7badcc5814d99455df46564f3c077"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/210577fe3cf7badcc5814d99455df46564f3c077", "reference": "210577fe3cf7badcc5814d99455df46564f3c077", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.13.2"}, "time": "2021-11-30T19:35:32+00:00"}, {"name": "nyholm/psr7", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "e874c8c4286a1e010fb4f385f3a55ac56a05cc93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/e874c8c4286a1e010fb4f385f3a55ac56a05cc93", "reference": "e874c8c4286a1e010fb4f385f3a55ac56a05cc93", "shasum": ""}, "require": {"php": ">=7.1", "php-http/message-factory": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "provide": {"php-http/message-factory-implementation": "1.0", "psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || 8.5 || 9.4", "symfony/error-handler": "^4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.6.1"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2023-04-17T16:03:48+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "pear/archive_tar", "version": "1.4.14", "source": {"type": "git", "url": "https://github.com/pear/Archive_Tar.git", "reference": "4d761c5334c790e45ef3245f0864b8955c562caa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Archive_Tar/zipball/4d761c5334c790e45ef3245f0864b8955c562caa", "reference": "4d761c5334c790e45ef3245f0864b8955c562caa", "shasum": ""}, "require": {"pear/pear-core-minimal": "^1.10.0alpha2", "php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-bz2": "Bz2 compression support.", "ext-xz": "Lzma2 compression support.", "ext-zlib": "Gzip compression support."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Archive_Tar": ""}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Tar file management class with compression support (gzip, bzip2, lzma2)", "homepage": "https://github.com/pear/Archive_Tar", "keywords": ["archive", "tar"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Archive_Tar", "source": "https://github.com/pear/Archive_Tar"}, "funding": [{"url": "https://github.com/mrook", "type": "github"}, {"url": "https://www.patreon.com/michielrook", "type": "patreon"}], "time": "2021-07-20T13:53:39+00:00"}, {"name": "pear/console_getopt", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/pear/Console_Getopt.git", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Console_Getopt/zipball/a41f8d3e668987609178c7c4a9fe48fecac53fa0", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Console": "./"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}, {"name": "Stig Bakken", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Helper"}], "description": "More info available on: http://pear.php.net/package/Console_<PERSON>opt", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Console_Getopt", "source": "https://github.com/pear/Console_<PERSON>opt"}, "time": "2019-11-20T18:27:48+00:00"}, {"name": "pear/pear-core-minimal", "version": "v1.10.11", "source": {"type": "git", "url": "https://github.com/pear/pear-core-minimal.git", "reference": "68d0d32ada737153b7e93b8d3c710ebe70ac867d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/pear-core-minimal/zipball/68d0d32ada737153b7e93b8d3c710ebe70ac867d", "reference": "68d0d32ada737153b7e93b8d3c710ebe70ac867d", "shasum": ""}, "require": {"pear/console_getopt": "~1.4", "pear/pear_exception": "~1.0"}, "replace": {"rsky/pear-core-min": "self.version"}, "type": "library", "autoload": {"psr-0": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["src/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}], "description": "Minimal set of PEAR core files to be used as composer dependency", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR", "source": "https://github.com/pear/pear-core-minimal"}, "time": "2021-08-10T22:31:03+00:00"}, {"name": "pear/pear_exception", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/pear/PEAR_Exception.git", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/PEAR_Exception/zipball/b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "<9"}, "type": "class", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["PEAR/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["."], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The PEAR Exception base class.", "homepage": "https://github.com/pear/PEAR_Exception", "keywords": ["exception"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR_Exception", "source": "https://github.com/pear/PEAR_Exception"}, "time": "2021-03-21T15:43:46+00:00"}, {"name": "pelago/emogrifier", "version": "v5.0.1", "source": {"type": "git", "url": "https://github.com/MyIntervals/emogrifier.git", "reference": "37595a9bb62c3c25969bdd9e8d7dd24c3ac62bc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/emogrifier/zipball/37595a9bb62c3c25969bdd9e8d7dd24c3ac62bc9", "reference": "37595a9bb62c3c25969bdd9e8d7dd24c3ac62bc9", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0", "symfony/css-selector": "^3.4.32 || ^4.4 || ^5.1"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2.0", "rawr/cross-data-providers": "^2.3.0", "slevomat/coding-standard": "^6.4.1", "squizlabs/php_codesniffer": "^3.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0.x-dev"}}, "autoload": {"psr-4": {"Pelago\\Emogrifier\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "Converts CSS styles into inline style attributes in your HTML code", "homepage": "https://www.myintervals.com/emogrifier.php", "keywords": ["css", "email", "pre-processing"], "support": {"issues": "https://github.com/MyIntervals/emogrifier/issues", "source": "https://github.com/MyIntervals/emogrifier"}, "time": "2021-04-06T08:18:22+00:00"}, {"name": "php-http/message-factory", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/a478cb11f66a6ac48d8954216cfed9aa06a501a1", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/master"}, "time": "2015-12-19T14:08:53+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.19.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf", "reference": "a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.2 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "dompdf/dompdf": "^1.0", "friendsofphp/php-cs-fixer": "^2.18", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^0.12.82", "phpstan/phpstan-phpunit": "^0.12.18", "phpunit/phpunit": "^8.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.19.0"}, "time": "2021-10-31T15:09:20+00:00"}, {"name": "pimple/pimple", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/silexphp/Pimple.git", "reference": "a94b3a4db7fb774b3d78dad2315ddc07629e1bed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silexphp/Pimple/zipball/a94b3a4db7fb774b3d78dad2315ddc07629e1bed", "reference": "a94b3a4db7fb774b3d78dad2315ddc07629e1bed", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1 || ^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^5.4@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4.x-dev"}}, "autoload": {"psr-0": {"Pimple": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON>, a simple Dependency Injection Container", "homepage": "https://pimple.symfony.com", "keywords": ["container", "dependency injection"], "support": {"source": "https://github.com/silexphp/Pimple/tree/v3.5.0"}, "time": "2021-10-28T11:13:42+00:00"}, {"name": "prestashop/autoload", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/autoload.git", "reference": "b84a116564d624e3db04fce648dbfee135df87c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/autoload/zipball/b84a116564d624e3db04fce648dbfee135df87c0", "reference": "b84a116564d624e3db04fce648dbfee135df87c0", "shasum": ""}, "require": {"php": ">=7.2", "symfony/filesystem": "~4.4|~5.4", "symfony/finder": "~4.4|~5.4", "symfony/polyfill-php80": "^v1.26.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^1.8.10", "phpunit/phpunit": "~8.5.30|^9.5"}, "type": "library", "autoload": {"psr-4": {"PrestaShop\\Autoload\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}, {"name": "PrestaShop Community", "homepage": "https://contributors.prestashop.com/"}], "description": "PrestaShop legacy autoloading", "support": {"source": "https://github.com/PrestaShop/autoload/tree/v1.0.1"}, "time": "2023-07-07T13:49:33+00:00"}, {"name": "prestashop/blockreassurance", "version": "v5.1.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/blockreassurance.git", "reference": "e39663c439e025fe27721b265b0493df6ca3d7e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/blockreassurance/zipball/e39663c439e025fe27721b265b0493df6ca3d7e7", "reference": "e39663c439e025fe27721b265b0493df6ca3d7e7", "shasum": ""}, "require": {"php": ">=5.6.0"}, "require-dev": {"prestashop/php-dev-tools": "^4.3"}, "type": "prestashop-module", "autoload": {"psr-4": {"PrestaShop\\Module\\BlockReassurance\\": "src/"}, "classmap": ["blockreassurance.php", "classes/ReassuranceActivity.php"], "exclude-from-classmap": []}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module blockreassurance", "homepage": "https://github.com/PrestaShop/blockreassurance", "support": {"source": "https://github.com/PrestaShop/blockreassurance/tree/v5.1.4"}, "time": "2023-11-08T09:53:12+00:00"}, {"name": "prestashop/blockwishlist", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/blockwishlist.git", "reference": "75c39bc12648cba4e09d870479e7857fd06eee79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/blockwishlist/zipball/75c39bc12648cba4e09d870479e7857fd06eee79", "reference": "75c39bc12648cba4e09d870479e7857fd06eee79", "shasum": ""}, "require-dev": {"prestashop/php-dev-tools": "^4.3"}, "type": "prestashop-module", "autoload": {"psr-4": {"PrestaShop\\Module\\BlockWishList\\": "src/"}, "classmap": ["blockwishlist.php", "controllers", "classes"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module blockwishlist", "homepage": "https://github.com/PrestaShop/blockwishlist", "support": {"source": "https://github.com/PrestaShop/blockwishlist/tree/v3.0.1"}, "time": "2023-08-11T10:08:33+00:00"}, {"name": "prestashop/circuit-breaker", "version": "v4.0.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/circuit-breaker.git", "reference": "8dff14c1411448d4d64b740d12100637ad64f5c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/circuit-breaker/zipball/8dff14c1411448d4d64b740d12100637ad64f5c7", "reference": "8dff14c1411448d4d64b740d12100637ad64f5c7", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.3"}, "require-dev": {"doctrine/cache": "^1.10.2", "phpunit/phpunit": "^8", "prestashop/php-dev-tools": "^4.1", "psr/simple-cache": "^1.0", "symfony/cache": "^4.4", "symfony/event-dispatcher": "^4.4"}, "suggest": {"doctrine/cache": "Allows use of Doctrine Cache adapters to store transactions", "ext-apcu": "Allows use of APCu adapter (performant) to store transactions", "symfony/cache": "Allows use of Symfony Cache adapters to store transactions"}, "type": "library", "autoload": {"psr-4": {"PrestaShop\\CircuitBreaker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}, {"name": "PrestaShop Community", "homepage": "http://contributors.prestashop.com/"}], "description": "A circuit breaker implementation for PHP", "support": {"issues": "https://github.com/PrestaShop/circuit-breaker/issues", "source": "https://github.com/PrestaShop/circuit-breaker/tree/v4.0.1"}, "time": "2021-10-12T15:22:50+00:00"}, {"name": "prestashop/classic", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/classic-theme.git", "reference": "efb5fa7aa2ca2fb856d5f38b720fb6d39f448526"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/classic-theme/zipball/efb5fa7aa2ca2fb856d5f38b720fb6d39f448526", "reference": "efb5fa7aa2ca2fb856d5f38b720fb6d39f448526", "shasum": ""}, "require-dev": {"symfony/console": "~4.4 || ^5.0", "symfony/yaml": "~4.4 || ^5.0"}, "type": "prestashop-theme", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}, {"name": "PrestaShop Community", "homepage": "https://contributors.prestashop.com/"}], "description": "Classic theme for PrestaShop 8.1", "support": {"source": "https://github.com/PrestaShop/classic-theme/tree/2.1.3"}, "time": "2024-02-26T16:00:42+00:00"}, {"name": "prestashop/contactform", "version": "v4.4.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/contactform.git", "reference": "98886a82afc2953cb166081e9eaf2872fc457d4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/contactform/zipball/98886a82afc2953cb166081e9eaf2872fc457d4d", "reference": "98886a82afc2953cb166081e9eaf2872fc457d4d", "shasum": ""}, "require": {"jakeasmith/http_build_url": "^1", "php": ">=5.4.0"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["contactform.php", "upgrade/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module contactform", "homepage": "https://github.com/PrestaShop/contactform", "support": {"source": "https://github.com/PrestaShop/contactform/tree/v4.4.2"}, "time": "2024-01-12T10:15:52+00:00"}, {"name": "prestashop/dashactivity", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/dashactivity.git", "reference": "e604732a897a26d751afac09936bdcb7eb7b9044"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/dashactivity/zipball/e604732a897a26d751afac09936bdcb7eb7b9044", "reference": "e604732a897a26d751afac09936bdcb7eb7b9044", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module dashactivity", "homepage": "https://github.com/PrestaShop/dashactivity", "support": {"source": "https://github.com/PrestaShop/dashactivity/tree/v2.1.1"}, "time": "2024-04-08T11:12:02+00:00"}, {"name": "prestashop/dashgoals", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/dashgoals.git", "reference": "13667ef6458cdbc55b760a1d9ae75ac76bb13932"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/dashgoals/zipball/13667ef6458cdbc55b760a1d9ae75ac76bb13932", "reference": "13667ef6458cdbc55b760a1d9ae75ac76bb13932", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["dashgoals.php", "controllers/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module dashgoals", "homepage": "https://github.com/PrestaShop/dashgoals", "support": {"source": "https://github.com/PrestaShop/dashgoals/tree/v2.0.4"}, "time": "2023-03-22T16:16:12+00:00"}, {"name": "prestashop/dashproducts", "version": "v2.1.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/dashproducts.git", "reference": "1e126b6ce089b5ee6c45eb5b87c5534c5348964b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/dashproducts/zipball/1e126b6ce089b5ee6c45eb5b87c5534c5348964b", "reference": "1e126b6ce089b5ee6c45eb5b87c5534c5348964b", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module dashproducts", "homepage": "https://github.com/PrestaShop/dashproducts", "support": {"source": "https://github.com/PrestaShop/dashproducts/tree/v2.1.4"}, "time": "2023-11-28T15:02:16+00:00"}, {"name": "prestashop/dashtrends", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/dashtrends.git", "reference": "c1661f19edfabbc584c725fecbf0d63d7595866b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/dashtrends/zipball/c1661f19edfabbc584c725fecbf0d63d7595866b", "reference": "c1661f19edfabbc584c725fecbf0d63d7595866b", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["dashtrends.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module dashtrends", "homepage": "https://github.com/PrestaShop/dashtrends", "support": {"source": "https://github.com/PrestaShop/dashtrends/tree/v2.1.3"}, "time": "2023-11-28T14:23:21+00:00"}, {"name": "prestashop/decimal", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/PrestaShop/decimal.git", "reference": "b5afdcc4b03140f838bb7b256aec6c21fd83951b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/decimal/zipball/b5afdcc4b03140f838bb7b256aec6c21fd83951b", "reference": "b5afdcc4b03140f838bb7b256aec6c21fd83951b", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"phpstan/phpstan": "^0.12.99", "phpunit/phpunit": "8.*", "prestashop/php-dev-tools": "^4.1"}, "type": "library", "autoload": {"psr-4": {"PrestaShop\\Decimal\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "Object-oriented wrapper/shim for BC Math PHP extension. Allows for arbitrary-precision math operations.", "homepage": "https://github.com/prestashop/decimal", "keywords": ["bcmath", "decimal", "math", "precision", "prestashop"], "support": {"issues": "https://github.com/PrestaShop/decimal/issues", "source": "https://github.com/PrestaShop/decimal/tree/1.5.0"}, "time": "2022-02-02T09:04:37+00:00"}, {"name": "prestashop/graphnvd3", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/graphnvd3.git", "reference": "3125226478226ce0cb2b3164709a0ca1450e6e62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/graphnvd3/zipball/3125226478226ce0cb2b3164709a0ca1450e6e62", "reference": "3125226478226ce0cb2b3164709a0ca1450e6e62", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module graphnvd3", "homepage": "https://github.com/PrestaShop/graphnvd3", "support": {"source": "https://github.com/PrestaShop/graphnvd3/tree/v2.0.3"}, "time": "2023-01-17T15:55:21+00:00"}, {"name": "prestashop/gridhtml", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/gridhtml.git", "reference": "ed319db979ddd8d41f6552d7196e6feaa11ad2c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/gridhtml/zipball/ed319db979ddd8d41f6552d7196e6feaa11ad2c2", "reference": "ed319db979ddd8d41f6552d7196e6feaa11ad2c2", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module gridhtml", "homepage": "https://github.com/PrestaShop/gridhtml", "support": {"source": "https://github.com/PrestaShop/gridhtml/tree/v2.0.3"}, "time": "2023-01-19T11:24:06+00:00"}, {"name": "prestashop/gsitemap", "version": "v4.4.0", "source": {"type": "git", "url": "https://github.com/PrestaShop/gsitemap.git", "reference": "2f9f69f9a06d864c7e7ed8a89cdee5d23b2c91ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/gsitemap/zipball/2f9f69f9a06d864c7e7ed8a89cdee5d23b2c91ca", "reference": "2f9f69f9a06d864c7e7ed8a89cdee5d23b2c91ca", "shasum": ""}, "require": {"php": ">=5.6.0"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "autoload": {"classmap": ["gsitemap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module gsitemap", "homepage": "https://github.com/PrestaShop/gsitemap", "support": {"source": "https://github.com/PrestaShop/gsitemap/tree/v4.4.0"}, "time": "2024-01-10T17:10:01+00:00"}, {"name": "prestashop/laminas-code-lts", "version": "dev-4.5-lts", "source": {"type": "git", "url": "https://github.com/PrestaShop/laminas-code-lts.git", "reference": "c22c7aee2092bcec532b910a1f4dbac0ce6a3bbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/laminas-code-lts/zipball/c22c7aee2092bcec532b910a1f4dbac0ce6a3bbf", "reference": "c22c7aee2092bcec532b910a1f4dbac0ce6a3bbf", "shasum": ""}, "require": {"php": ">=7.2, <8.2"}, "replace": {"laminas/laminas-code": "^4.5"}, "require-dev": {"doctrine/annotations": "^1.13.2", "ext-phar": "*", "laminas/laminas-coding-standard": "^2.1.0", "laminas/laminas-stdlib": "^3.2.1", "phpunit/phpunit": "^8.5.26", "psalm/plugin-phpunit": "^0.16.1", "vimeo/psalm": "^4.13.1"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "laminas/laminas-stdlib": "Laminas\\Stdlib component"}, "default-branch": true, "type": "library", "extra": {"thanks": {"name": "laminas/laminas-code", "url": "https://github.com/laminas/laminas-code"}}, "autoload": {"files": ["polyfill/ReflectionEnumPolyfill.php"], "psr-4": {"Laminas\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Adding support for a wider range of PHP versions to laminas/laminas-code", "homepage": "https://github.com/prestashop/laminas-code-lts", "keywords": ["code", "laminas", "laminasframework"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-code/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-code/issues", "rss": "https://github.com/laminas/laminas-code/releases.atom", "source": "https://github.com/laminas/laminas-code"}, "time": "2023-06-13T07:18:53+00:00"}, {"name": "prestashop/pagesnotfound", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/pagesnotfound.git", "reference": "cf05fc7770f9beff2a5c2f17bdfe2da7652744db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/pagesnotfound/zipball/cf05fc7770f9beff2a5c2f17bdfe2da7652744db", "reference": "cf05fc7770f9beff2a5c2f17bdfe2da7652744db", "shasum": ""}, "require": {"php": ">=5.6.0"}, "require-dev": {"prestashop/php-dev-tools": "^4.3"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module pagesnotfound", "homepage": "https://github.com/PrestaShop/pagesnotfound", "support": {"source": "https://github.com/PrestaShop/pagesnotfound/tree/v2.0.3"}, "time": "2023-06-16T10:59:52+00:00"}, {"name": "prestashop/productcomments", "version": "v7.0.0", "source": {"type": "git", "url": "https://github.com/PrestaShop/productcomments.git", "reference": "7287476cb44969af58721cb9ad7b5cb89393b2c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/productcomments/zipball/7287476cb44969af58721cb9ad7b5cb89393b2c8", "reference": "7287476cb44969af58721cb9ad7b5cb89393b2c8", "shasum": ""}, "require": {"php": ">=7.1.3"}, "require-dev": {"prestashop/php-dev-tools": "^4.3"}, "type": "prestashop-module", "autoload": {"psr-4": {"PrestaShop\\Module\\ProductComment\\": "src/"}, "classmap": ["productcomments.php"], "exclude-from-classmap": []}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module productcomments", "homepage": "https://github.com/PrestaShop/productcomments", "support": {"source": "https://github.com/PrestaShop/productcomments/tree/v7.0.0"}, "time": "2024-04-24T14:10:24+00:00"}, {"name": "prestashop/ps_banner", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_banner.git", "reference": "1eceeb26a551001ba531c95b2645aa272a9e6278"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_banner/zipball/1eceeb26a551001ba531c95b2645aa272a9e6278", "reference": "1eceeb26a551001ba531c95b2645aa272a9e6278", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_banner.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_banner", "homepage": "https://github.com/PrestaShop/ps_banner", "support": {"source": "https://github.com/PrestaShop/ps_banner/tree/v2.1.2"}, "time": "2022-07-18T14:20:33+00:00"}, {"name": "prestashop/ps_bestsellers", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_bestsellers.git", "reference": "bd1e97fa82c9953bdca738946aa624a5667c6a27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_bestsellers/zipball/bd1e97fa82c9953bdca738946aa624a5667c6a27", "reference": "bd1e97fa82c9953bdca738946aa624a5667c6a27", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Best Sellers", "homepage": "https://github.com/PrestaShop/ps_bestsellers", "support": {"source": "https://github.com/PrestaShop/ps_bestsellers/tree/v1.0.6"}, "time": "2023-02-28T10:35:02+00:00"}, {"name": "prestashop/ps_brandlist", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_brandlist.git", "reference": "890837532c14e8576a79df94d66d36dc46349b05"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_brandlist/zipball/890837532c14e8576a79df94d66d36dc46349b05", "reference": "890837532c14e8576a79df94d66d36dc46349b05", "shasum": ""}, "require": {"php": ">=5.4.0"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Brand list", "homepage": "https://github.com/PrestaShop/ps_brandlist", "support": {"issues": "https://github.com/PrestaShop/ps_brandlist/issues", "source": "https://github.com/PrestaShop/ps_brandlist/tree/v1.0.3"}, "time": "2022-01-20T14:52:25+00:00"}, {"name": "prestashop/ps_cashondelivery", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_cashondelivery.git", "reference": "1f80133e367cdffb5f3321ff1b47d014b537223c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_cashondelivery/zipball/1f80133e367cdffb5f3321ff1b47d014b537223c", "reference": "1f80133e367cdffb5f3321ff1b47d014b537223c", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_cashondelivery.php", "controllers"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_cashondelivery", "homepage": "https://github.com/PrestaShop/ps_cashondelivery", "support": {"source": "https://github.com/PrestaShop/ps_cashondelivery/tree/v2.0.1"}, "time": "2022-07-15T15:20:08+00:00"}, {"name": "prestashop/ps_categoryproducts", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_categoryproducts.git", "reference": "da028e09597270e60fd19b4de7303ada04b7dc74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_categoryproducts/zipball/da028e09597270e60fd19b4de7303ada04b7dc74", "reference": "da028e09597270e60fd19b4de7303ada04b7dc74", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_categoryproducts", "homepage": "https://github.com/PrestaShop/ps_categoryproducts", "support": {"source": "https://github.com/PrestaShop/ps_categoryproducts/tree/v1.0.7"}, "time": "2023-02-21T16:03:42+00:00"}, {"name": "prestashop/ps_categorytree", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_categorytree.git", "reference": "26a3caa0df0fc43933f50fdca829fd763badb8a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_categorytree/zipball/26a3caa0df0fc43933f50fdca829fd763badb8a0", "reference": "26a3caa0df0fc43933f50fdca829fd763badb8a0", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_categorytree.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop category tree links", "homepage": "https://github.com/PrestaShop/ps_categorytree", "support": {"source": "https://github.com/PrestaShop/ps_categorytree/tree/v2.0.3"}, "time": "2022-11-16T16:25:45+00:00"}, {"name": "prestashop/ps_checkpayment", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_checkpayment.git", "reference": "030a828cbcade2b381c8613775bdd4460852158d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_checkpayment/zipball/030a828cbcade2b381c8613775bdd4460852158d", "reference": "030a828cbcade2b381c8613775bdd4460852158d", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_checkpayment.php", "controllers/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_checkpayment", "homepage": "https://github.com/PrestaShop/ps_checkpayment", "support": {"source": "https://github.com/PrestaShop/ps_checkpayment/tree/v2.1.0"}, "time": "2023-11-17T14:46:55+00:00"}, {"name": "prestashop/ps_contactinfo", "version": "v3.3.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_contactinfo.git", "reference": "4437793934d46e3d30f8b51ea6df1af3ef03dd75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_contactinfo/zipball/4437793934d46e3d30f8b51ea6df1af3ef03dd75", "reference": "4437793934d46e3d30f8b51ea6df1af3ef03dd75", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_contactinfo", "homepage": "https://github.com/PrestaShop/ps_contactinfo", "support": {"source": "https://github.com/PrestaShop/ps_contactinfo/tree/v3.3.2"}, "time": "2023-03-09T21:06:14+00:00"}, {"name": "prestashop/ps_crossselling", "version": "v2.0.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_crossselling.git", "reference": "c0c5cfe8ac4e5e2840709406d0908a7245d8304a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_crossselling/zipball/c0c5cfe8ac4e5e2840709406d0908a7245d8304a", "reference": "c0c5cfe8ac4e5e2840709406d0908a7245d8304a", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_crossselling.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Cross selling", "homepage": "https://github.com/PrestaShop/ps_crossselling", "support": {"source": "https://github.com/PrestaShop/ps_crossselling/tree/v2.0.2"}, "time": "2023-02-22T10:44:49+00:00"}, {"name": "prestashop/ps_currencyselector", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_currencyselector.git", "reference": "3d19c025b7157699ee07233b9f0283fc0a123706"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_currencyselector/zipball/3d19c025b7157699ee07233b9f0283fc0a123706", "reference": "3d19c025b7157699ee07233b9f0283fc0a123706", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_currencyselector.php"], "exclude-from-classmap": []}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_currencyselector", "homepage": "https://github.com/PrestaShop/ps_currencyselector", "support": {"source": "https://github.com/PrestaShop/ps_currencyselector/tree/v2.1.1"}, "time": "2023-02-03T14:47:22+00:00"}, {"name": "prestashop/ps_customeraccountlinks", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_customeraccountlinks.git", "reference": "efd981fe07922cf8f847a08017b81464b90ba748"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_customeraccountlinks/zipball/efd981fe07922cf8f847a08017b81464b90ba748", "reference": "efd981fe07922cf8f847a08017b81464b90ba748", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_customeraccountlinks.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_customeraccountlinks", "homepage": "https://github.com/PrestaShop/ps_customeraccountlinks", "support": {"source": "https://github.com/PrestaShop/ps_customeraccountlinks/tree/v3.2.0"}, "time": "2023-01-31T09:10:00+00:00"}, {"name": "prestashop/ps_customersignin", "version": "v2.0.5", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_customersignin.git", "reference": "88016f3c1b4ba61a3f881bb295fefe4eef7edc92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_customersignin/zipball/88016f3c1b4ba61a3f881bb295fefe4eef7edc92", "reference": "88016f3c1b4ba61a3f881bb295fefe4eef7edc92", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_customersignin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Customer 'Sign in' link", "homepage": "https://github.com/PrestaShop/ps_customersignin", "support": {"source": "https://github.com/PrestaShop/ps_customersignin/tree/v2.0.5"}, "time": "2022-08-16T19:43:00+00:00"}, {"name": "prestashop/ps_customtext", "version": "v4.2.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_customtext.git", "reference": "9aa81fe7ffd5dacd161cbaac30bd7086a1de3178"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_customtext/zipball/9aa81fe7ffd5dacd161cbaac30bd7086a1de3178", "reference": "9aa81fe7ffd5dacd161cbaac30bd7086a1de3178", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_customtext.php", "classes/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_customtext", "homepage": "https://github.com/PrestaShop/ps_customtext", "support": {"source": "https://github.com/PrestaShop/ps_customtext/tree/v4.2.1"}, "time": "2023-01-16T16:58:49+00:00"}, {"name": "prestashop/ps_dataprivacy", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_dataprivacy.git", "reference": "5d9187b6d8df31c9d90668d0ce61aec86d50ba95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_dataprivacy/zipball/5d9187b6d8df31c9d90668d0ce61aec86d50ba95", "reference": "5d9187b6d8df31c9d90668d0ce61aec86d50ba95", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Data privacy", "homepage": "https://github.com/PrestaShop/ps_dataprivacy", "support": {"source": "https://github.com/PrestaShop/ps_dataprivacy/tree/v2.1.1"}, "time": "2023-01-18T15:19:08+00:00"}, {"name": "prestashop/ps_distributionapiclient", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_distributionapiclient.git", "reference": "4db438b616fb26b9281607db436e60f382fb2391"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_distributionapiclient/zipball/4db438b616fb26b9281607db436e60f382fb2391", "reference": "4db438b616fb26b9281607db436e60f382fb2391", "shasum": ""}, "require": {"doctrine/cache": "^2.1", "php": ">=7.2.5", "prestashop/circuit-breaker": "^4.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "phpunit/phpunit": "^8.5", "prestashop/php-dev-tools": "^4.2"}, "type": "prestashop-module", "autoload": {"psr-4": {"PrestaShop\\Module\\DistributionApiClient\\": "src/"}, "classmap": ["ps_distributionapiclient.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Modules from distribution API", "homepage": "https://github.com/PrestaShop/ps_distributionapiclient", "support": {"issues": "https://github.com/PrestaShop/ps_distributionapiclient/issues", "source": "https://github.com/PrestaShop/ps_distributionapiclient/tree/v1.1.1"}, "time": "2024-01-19T16:20:17+00:00"}, {"name": "prestashop/ps_emailalerts", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_emailalerts.git", "reference": "62fd97e7c9dcc21b5a9c547dc484ac1d3c191bfd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_emailalerts/zipball/62fd97e7c9dcc21b5a9c547dc484ac1d3c191bfd", "reference": "62fd97e7c9dcc21b5a9c547dc484ac1d3c191bfd", "shasum": ""}, "require": {"php": ">=7.1.3"}, "require-dev": {"prestashop/php-dev-tools": "^4.3"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_emailalerts", "homepage": "https://github.com/PrestaShop/ps_emailalerts", "support": {"source": "https://github.com/PrestaShop/ps_emailalerts/tree/v3.0.0"}, "time": "2024-01-10T11:22:50+00:00"}, {"name": "prestashop/ps_emailsubscription", "version": "v2.8.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_emailsubscription.git", "reference": "ce4e159df07b75423abf6804938dddb51b37a1fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_emailsubscription/zipball/ce4e159df07b75423abf6804938dddb51b37a1fa", "reference": "ce4e159df07b75423abf6804938dddb51b37a1fa", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_emailsubscription.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_emailsubscription", "homepage": "https://github.com/PrestaShop/ps_emailsubscription", "support": {"source": "https://github.com/PrestaShop/ps_emailsubscription/tree/v2.8.2"}, "time": "2024-03-07T09:34:54+00:00"}, {"name": "prestashop/ps_facetedsearch", "version": "v3.16.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_facetedsearch.git", "reference": "a516b0b46796e95246537b94df3e778fd19292f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_facetedsearch/zipball/a516b0b46796e95246537b94df3e778fd19292f9", "reference": "a516b0b46796e95246537b94df3e778fd19292f9", "shasum": ""}, "require": {"doctrine/collections": "^1.4", "php": ">=5.6"}, "require-dev": {"mockery/mockery": "^1.2", "phpunit/phpunit": "~5.7", "prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "autoload": {"psr-4": {"PrestaShop\\Module\\FacetedSearch\\": "src/", "PrestaShop\\Module\\FacetedSearch\\Tests\\": "tests/php/FacetedSearch", "PrestaShop\\Module\\FacetedSearch\\Controller\\": "src/Controller/"}, "classmap": ["ps_facetedsearch.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_facetedsearch", "homepage": "https://github.com/PrestaShop/ps_facetedsearch", "support": {"source": "https://github.com/PrestaShop/ps_facetedsearch/tree/v3.16.1"}, "time": "2024-06-24T08:31:50+00:00"}, {"name": "prestashop/ps_faviconnotificationbo", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_faviconnotificationbo.git", "reference": "1f077c2eb47037a3ad7ae8381684c341b7042942"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_faviconnotificationbo/zipball/1f077c2eb47037a3ad7ae8381684c341b7042942", "reference": "1f077c2eb47037a3ad7ae8381684c341b7042942", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "autoload": {"classmap": ["controllers", "ps_faviconnotificationbo.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Favicon notification BO", "homepage": "https://github.com/PrestaShop/ps_faviconnotificationbo", "support": {"source": "https://github.com/PrestaShop/ps_faviconnotificationbo/tree/v2.1.3"}, "time": "2023-02-03T14:47:24+00:00"}, {"name": "prestashop/ps_featuredproducts", "version": "v2.1.5", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_featuredproducts.git", "reference": "7a2af5e20c987821c926ef8a8fdb502dfe07b533"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_featuredproducts/zipball/7a2af5e20c987821c926ef8a8fdb502dfe07b533", "reference": "7a2af5e20c987821c926ef8a8fdb502dfe07b533", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_featuredproducts.php"], "exclude-from-classmap": []}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Featured products", "homepage": "https://github.com/PrestaShop/ps_featuredproducts", "support": {"source": "https://github.com/PrestaShop/ps_featuredproducts/tree/v2.1.5"}, "time": "2023-10-02T09:29:44+00:00"}, {"name": "prestashop/ps_googleanalytics", "version": "v5.0.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_googleanalytics.git", "reference": "2aa643fe04dafb32d55a80b2835a374031714f1c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_googleanalytics/zipball/2aa643fe04dafb32d55a80b2835a374031714f1c", "reference": "2aa643fe04dafb32d55a80b2835a374031714f1c", "shasum": ""}, "require": {"php": ">=7.1.3"}, "require-dev": {"prestashop/php-dev-tools": "3.*"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_googleanalytics.php", "controllers", "classes"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_googleanalytics", "homepage": "https://github.com/PrestaShop/ps_googleanalytics", "support": {"source": "https://github.com/PrestaShop/ps_googleanalytics/tree/v5.0.2"}, "time": "2024-03-04T15:47:09+00:00"}, {"name": "prestashop/ps_imageslider", "version": "v3.1.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_imageslider.git", "reference": "25c86742d2e0d3e1abc778a82f127b04379fa164"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_imageslider/zipball/25c86742d2e0d3e1abc778a82f127b04379fa164", "reference": "25c86742d2e0d3e1abc778a82f127b04379fa164", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"prestashop/php-dev-tools": "^4.3"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_imageslider.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Image slider", "homepage": "https://github.com/PrestaShop/ps_imageslider", "support": {"source": "https://github.com/PrestaShop/ps_imageslider/tree/v3.1.4"}, "time": "2024-01-16T08:14:40+00:00"}, {"name": "prestashop/ps_languageselector", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_languageselector.git", "reference": "83b165f03432292d9e142a7de944f23233f11ae0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_languageselector/zipball/83b165f03432292d9e142a7de944f23233f11ae0", "reference": "83b165f03432292d9e142a7de944f23233f11ae0", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_languageselector.php"], "exclude-from-classmap": []}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_languageselector", "homepage": "https://github.com/PrestaShop/ps_languageselector", "support": {"source": "https://github.com/PrestaShop/ps_languageselector/tree/v2.1.3"}, "time": "2023-03-17T13:00:03+00:00"}, {"name": "prestashop/ps_linklist", "version": "v6.0.7", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_linklist.git", "reference": "a7696f34e8f56d0746be2d52eb0bb32ffd37985b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_linklist/zipball/a7696f34e8f56d0746be2d52eb0bb32ffd37985b", "reference": "a7696f34e8f56d0746be2d52eb0bb32ffd37985b", "shasum": ""}, "require": {"php": ">=7.2.5"}, "require-dev": {"prestashop/php-dev-tools": "^4.3"}, "type": "prestashop-module", "autoload": {"psr-4": {"PrestaShop\\Module\\LinkList\\": "src/"}, "classmap": ["ps_linklist.php"], "exclude-from-classmap": []}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Link list", "homepage": "https://github.com/PrestaShop/ps_linklist", "support": {"source": "https://github.com/PrestaShop/ps_linklist/tree/v6.0.7"}, "time": "2024-02-28T13:48:54+00:00"}, {"name": "prestashop/ps_mainmenu", "version": "v2.3.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_mainmenu.git", "reference": "d6c0dfdad45aa63d5cff86dc20a242804324da50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_mainmenu/zipball/d6c0dfdad45aa63d5cff86dc20a242804324da50", "reference": "d6c0dfdad45aa63d5cff86dc20a242804324da50", "shasum": ""}, "require": {"php": ">=5.6.0"}, "require-dev": {"prestashop/php-dev-tools": "^4.3"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_mainmenu.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Main menu", "homepage": "https://github.com/PrestaShop/ps_mainmenu", "support": {"source": "https://github.com/PrestaShop/ps_mainmenu/tree/v2.3.4"}, "time": "2024-01-10T11:22:28+00:00"}, {"name": "prestashop/ps_newproducts", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_newproducts.git", "reference": "09ad0fa724af4e11664978365a0748c295ec2e8b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_newproducts/zipball/09ad0fa724af4e11664978365a0748c295ec2e8b", "reference": "09ad0fa724af4e11664978365a0748c295ec2e8b", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - New products", "homepage": "https://github.com/PrestaShop/ps_newproducts", "support": {"issues": "https://github.com/PrestaShop/ps_newproducts/issues", "source": "https://github.com/PrestaShop/ps_newproducts/tree/v1.0.4"}, "time": "2023-02-21T21:49:28+00:00"}, {"name": "prestashop/ps_searchbar", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_searchbar.git", "reference": "e02b6b2b5e6084227f3a2b7cc9272867a23843f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_searchbar/zipball/e02b6b2b5e6084227f3a2b7cc9272867a23843f1", "reference": "e02b6b2b5e6084227f3a2b7cc9272867a23843f1", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_searchbar", "homepage": "https://github.com/PrestaShop/ps_searchbar", "support": {"source": "https://github.com/PrestaShop/ps_searchbar/tree/v2.1.3"}, "time": "2022-04-13T10:56:58+00:00"}, {"name": "prestashop/ps_sharebuttons", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_sharebuttons.git", "reference": "781b63a4cb0ea71989c7396d0b928c7294664852"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_sharebuttons/zipball/781b63a4cb0ea71989c7396d0b928c7294664852", "reference": "781b63a4cb0ea71989c7396d0b928c7294664852", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_sharebuttons", "homepage": "https://github.com/PrestaShop/ps_sharebuttons", "support": {"source": "https://github.com/PrestaShop/ps_sharebuttons/tree/v2.1.2"}, "time": "2022-12-13T10:10:47+00:00"}, {"name": "prestashop/ps_shoppingcart", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_shoppingcart.git", "reference": "6f3845a09f62b13439ad22def1987b1efd28a529"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_shoppingcart/zipball/6f3845a09f62b13439ad22def1987b1efd28a529", "reference": "6f3845a09f62b13439ad22def1987b1efd28a529", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"prestashop/php-dev-tools": "~3.0"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_shoppingcart.php", "controllers/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_shoppingcart", "homepage": "https://github.com/PrestaShop/ps_shoppingcart", "support": {"source": "https://github.com/PrestaShop/ps_shoppingcart/tree/v3.0.0"}, "time": "2023-11-20T14:35:44+00:00"}, {"name": "prestashop/ps_socialfollow", "version": "v2.3.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_socialfollow.git", "reference": "2819f3a66da5160374aa91db9025eefecdbd122c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_socialfollow/zipball/2819f3a66da5160374aa91db9025eefecdbd122c", "reference": "2819f3a66da5160374aa91db9025eefecdbd122c", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_socialfollow", "homepage": "https://github.com/PrestaShop/ps_socialfollow", "support": {"source": "https://github.com/PrestaShop/ps_socialfollow/tree/v2.3.2"}, "time": "2024-01-10T11:23:11+00:00"}, {"name": "prestashop/ps_specials", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_specials.git", "reference": "b8cb0c931f98eaacf59044550f8ca195f3f5bde1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_specials/zipball/b8cb0c931f98eaacf59044550f8ca195f3f5bde1", "reference": "b8cb0c931f98eaacf59044550f8ca195f3f5bde1", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Specials", "homepage": "https://github.com/PrestaShop/ps_specials", "support": {"source": "https://github.com/PrestaShop/ps_specials/tree/v1.0.2"}, "time": "2023-01-18T13:48:05+00:00"}, {"name": "prestashop/ps_supplierlist", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_supplierlist.git", "reference": "89a555f59d330638ed26fdada3829b1321e9b775"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_supplierlist/zipball/89a555f59d330638ed26fdada3829b1321e9b775", "reference": "89a555f59d330638ed26fdada3829b1321e9b775", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Supplier List", "homepage": "https://github.com/PrestaShop/ps_supplierlist", "support": {"issues": "https://github.com/PrestaShop/ps_supplierlist/issues", "source": "https://github.com/PrestaShop/ps_supplierlist/tree/v1.0.6"}, "time": "2023-02-03T14:47:42+00:00"}, {"name": "prestashop/ps_themecusto", "version": "v1.2.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_themecusto.git", "reference": "f6d61045450d0e238c469b15ce699a694ddcf3f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_themecusto/zipball/f6d61045450d0e238c469b15ce699a694ddcf3f7", "reference": "f6d61045450d0e238c469b15ce699a694ddcf3f7", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_themecusto.php", "controllers", "classes"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_themecusto", "homepage": "https://github.com/PrestaShop/ps_themecusto", "support": {"source": "https://github.com/PrestaShop/ps_themecusto/tree/v1.2.4"}, "time": "2024-03-07T09:35:26+00:00"}, {"name": "prestashop/ps_viewedproduct", "version": "v1.2.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_viewedproduct.git", "reference": "79ea14e3e659089a9f4ab410296bcff734d79960"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_viewedproduct/zipball/79ea14e3e659089a9f4ab410296bcff734d79960", "reference": "79ea14e3e659089a9f4ab410296bcff734d79960", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop - Viewed Products", "homepage": "https://github.com/PrestaShop/ps_viewedproduct", "support": {"source": "https://github.com/PrestaShop/ps_viewedproduct/tree/v1.2.4"}, "time": "2023-02-20T16:31:56+00:00"}, {"name": "prestashop/ps_wirepayment", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/PrestaShop/ps_wirepayment.git", "reference": "3b75b0c99206b96405248aa82e6a3699dc57993e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/ps_wirepayment/zipball/3b75b0c99206b96405248aa82e6a3699dc57993e", "reference": "3b75b0c99206b96405248aa82e6a3699dc57993e", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "autoload": {"classmap": ["ps_wirepayment.php"], "exclude-from-classmap": []}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module ps_wirepayment", "homepage": "https://github.com/PrestaShop/ps_wirepayment", "support": {"source": "https://github.com/PrestaShop/ps_wirepayment/tree/v2.2.0"}, "time": "2024-02-29T10:19:58+00:00"}, {"name": "prestashop/psgdpr", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/psgdpr.git", "reference": "2b94cd472d00c837275f0aa8677947de2691c6d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/psgdpr/zipball/2b94cd472d00c837275f0aa8677947de2691c6d7", "reference": "2b94cd472d00c837275f0aa8677947de2691c6d7", "shasum": ""}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "autoload": {"classmap": ["classes", "controllers", "psgdpr.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module psgdpr", "homepage": "https://github.com/PrestaShopCorp/psgdpr", "support": {"source": "https://github.com/PrestaShop/psgdpr/tree/v1.4.3"}, "time": "2022-12-15T10:02:36+00:00"}, {"name": "prestashop/statsbestcategories", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsbestcategories.git", "reference": "fee7b7c679ec6cdec8fe643f8dbc4399f6008e60"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsbestcategories/zipball/fee7b7c679ec6cdec8fe643f8dbc4399f6008e60", "reference": "fee7b7c679ec6cdec8fe643f8dbc4399f6008e60", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsbestcategories", "homepage": "https://github.com/PrestaShop/statsbestcategories", "support": {"source": "https://github.com/PrestaShop/statsbestcategories/tree/v2.0.1"}, "time": "2022-01-06T18:39:43+00:00"}, {"name": "prestashop/statsbestcustomers", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsbestcustomers.git", "reference": "53568b28fea462b1cdce89bac6860a68ac100015"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsbestcustomers/zipball/53568b28fea462b1cdce89bac6860a68ac100015", "reference": "53568b28fea462b1cdce89bac6860a68ac100015", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsbestcustomers", "homepage": "https://github.com/PrestaShop/statsbestcustomers", "support": {"source": "https://github.com/PrestaShop/statsbestcustomers/tree/v2.0.4"}, "time": "2023-01-16T13:22:22+00:00"}, {"name": "prestashop/statsbestmanufacturers", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsbestmanufacturers.git", "reference": "77f610b556034e6828a245472ac1bdb6ffa28cad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsbestmanufacturers/zipball/77f610b556034e6828a245472ac1bdb6ffa28cad", "reference": "77f610b556034e6828a245472ac1bdb6ffa28cad", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsbestmanufacturers", "homepage": "https://github.com/PrestaShop/statsbestmanufacturers", "support": {"source": "https://github.com/PrestaShop/statsbestmanufacturers/tree/v2.0.3"}, "time": "2023-03-22T16:16:33+00:00"}, {"name": "prestashop/statsbestproducts", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsbestproducts.git", "reference": "0f5e03455cce6f6b0798948a67c26b413227d25a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsbestproducts/zipball/0f5e03455cce6f6b0798948a67c26b413227d25a", "reference": "0f5e03455cce6f6b0798948a67c26b413227d25a", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsbestproducts", "homepage": "https://github.com/PrestaShop/statsbestproducts", "support": {"source": "https://github.com/PrestaShop/statsbestproducts/tree/v2.0.1"}, "time": "2022-01-11T07:52:37+00:00"}, {"name": "prestashop/statsbestsuppliers", "version": "v2.0.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsbestsuppliers.git", "reference": "07c685bb744b71306f72c68660092ddb5fa47d46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsbestsuppliers/zipball/07c685bb744b71306f72c68660092ddb5fa47d46", "reference": "07c685bb744b71306f72c68660092ddb5fa47d46", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsbestsuppliers", "homepage": "https://github.com/PrestaShop/statsbestsuppliers", "support": {"source": "https://github.com/PrestaShop/statsbestsuppliers/tree/v2.0.2"}, "time": "2023-03-22T09:07:44+00:00"}, {"name": "prestashop/statsbestvouchers", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsbestvouchers.git", "reference": "9b9a8e1af451541e021c2af17c60e943fe594559"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsbestvouchers/zipball/9b9a8e1af451541e021c2af17c60e943fe594559", "reference": "9b9a8e1af451541e021c2af17c60e943fe594559", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsbestvouchers", "homepage": "https://github.com/PrestaShop/statsbestvouchers", "support": {"source": "https://github.com/PrestaShop/statsbestvouchers/tree/v2.0.1"}, "time": "2022-01-10T14:25:22+00:00"}, {"name": "prestashop/statscarrier", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/statscarrier.git", "reference": "9f4cc5c7dbcc6f08be0aa2e7e6dc333d1ea665dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statscarrier/zipball/9f4cc5c7dbcc6f08be0aa2e7e6dc333d1ea665dc", "reference": "9f4cc5c7dbcc6f08be0aa2e7e6dc333d1ea665dc", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statscarrier", "homepage": "https://github.com/PrestaShop/statscarrier", "support": {"source": "https://github.com/PrestaShop/statscarrier/tree/v2.0.1"}, "time": "2022-01-11T07:50:23+00:00"}, {"name": "prestashop/statscatalog", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/statscatalog.git", "reference": "ba29de8fc58a0ab1f80af62ee964f65cf371f427"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statscatalog/zipball/ba29de8fc58a0ab1f80af62ee964f65cf371f427", "reference": "ba29de8fc58a0ab1f80af62ee964f65cf371f427", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statscatalog", "homepage": "https://github.com/PrestaShop/statscatalog", "support": {"source": "https://github.com/PrestaShop/statscatalog/tree/v2.0.4"}, "time": "2023-11-28T15:22:40+00:00"}, {"name": "prestashop/statscheckup", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/statscheckup.git", "reference": "0535d263a79e3357ea4f0d6dfba5d3cff9044584"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statscheckup/zipball/0535d263a79e3357ea4f0d6dfba5d3cff9044584", "reference": "0535d263a79e3357ea4f0d6dfba5d3cff9044584", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statscheckup", "homepage": "https://github.com/PrestaShop/statscheckup", "support": {"source": "https://github.com/PrestaShop/statscheckup/tree/v2.0.3"}, "time": "2023-11-28T14:23:23+00:00"}, {"name": "prestashop/statsdata", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsdata.git", "reference": "0ea922363c3ee786518f7185c21104edc3525f6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsdata/zipball/0ea922363c3ee786518f7185c21104edc3525f6f", "reference": "0ea922363c3ee786518f7185c21104edc3525f6f", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsdata", "homepage": "https://github.com/PrestaShop/statsdata", "support": {"source": "https://github.com/PrestaShop/statsdata/tree/v2.1.1"}, "time": "2023-01-11T10:19:56+00:00"}, {"name": "prestashop/statsforecast", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsforecast.git", "reference": "9f2ab9f6ca1bf35ee5f108c5f2ed346d3f8b9b73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsforecast/zipball/9f2ab9f6ca1bf35ee5f108c5f2ed346d3f8b9b73", "reference": "9f2ab9f6ca1bf35ee5f108c5f2ed346d3f8b9b73", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsforecast", "homepage": "https://github.com/PrestaShop/statsforecast", "support": {"source": "https://github.com/PrestaShop/statsforecast/tree/v2.0.4"}, "time": "2022-02-03T14:49:26+00:00"}, {"name": "prestashop/statsnewsletter", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsnewsletter.git", "reference": "cca041983fee792469f865c9709f8f1a4cd4591c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsnewsletter/zipball/cca041983fee792469f865c9709f8f1a4cd4591c", "reference": "cca041983fee792469f865c9709f8f1a4cd4591c", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsnewsletter", "homepage": "https://github.com/PrestaShop/statsnewsletter", "support": {"source": "https://github.com/PrestaShop/statsnewsletter/tree/v2.0.3"}, "time": "2022-01-11T16:01:42+00:00"}, {"name": "prestashop/statspersonalinfos", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/statspersonalinfos.git", "reference": "f0dfeccf98e831287db9a66bee6785135fbba643"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statspersonalinfos/zipball/f0dfeccf98e831287db9a66bee6785135fbba643", "reference": "f0dfeccf98e831287db9a66bee6785135fbba643", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statspersonalinfos", "homepage": "https://github.com/PrestaShop/statspersonalinfos", "support": {"source": "https://github.com/PrestaShop/statspersonalinfos/tree/v2.0.4"}, "time": "2022-01-10T14:47:14+00:00"}, {"name": "prestashop/statsproduct", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsproduct.git", "reference": "56b344e6d8fd303d9e5742b7bd258534ddff3c00"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsproduct/zipball/56b344e6d8fd303d9e5742b7bd258534ddff3c00", "reference": "56b344e6d8fd303d9e5742b7bd258534ddff3c00", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsproduct", "homepage": "https://github.com/PrestaShop/statsproduct", "support": {"source": "https://github.com/PrestaShop/statsproduct/tree/v2.1.3"}, "time": "2023-11-27T15:50:20+00:00"}, {"name": "prestashop/statsregistrations", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsregistrations.git", "reference": "efdc328afa96ffed859fb8333a3a18b9a86e1c4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsregistrations/zipball/efdc328afa96ffed859fb8333a3a18b9a86e1c4a", "reference": "efdc328afa96ffed859fb8333a3a18b9a86e1c4a", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsregistrations", "homepage": "https://github.com/PrestaShop/statsregistrations", "support": {"source": "https://github.com/PrestaShop/statsregistrations/tree/v2.0.1"}, "time": "2022-01-04T13:38:03+00:00"}, {"name": "prestashop/statssales", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/PrestaShop/statssales.git", "reference": "35a14881b522bf442ff295dff1d673c90c9e8f10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statssales/zipball/35a14881b522bf442ff295dff1d673c90c9e8f10", "reference": "35a14881b522bf442ff295dff1d673c90c9e8f10", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statssales", "homepage": "https://github.com/PrestaShop/statssales", "support": {"source": "https://github.com/PrestaShop/statssales/tree/v2.1.0"}, "time": "2022-04-08T12:58:57+00:00"}, {"name": "prestashop/statssearch", "version": "v2.0.2", "source": {"type": "git", "url": "https://github.com/PrestaShop/statssearch.git", "reference": "10fe164304f1cf3d230f04c917d53e8859fed203"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statssearch/zipball/10fe164304f1cf3d230f04c917d53e8859fed203", "reference": "10fe164304f1cf3d230f04c917d53e8859fed203", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statssearch", "homepage": "https://github.com/PrestaShop/statssearch", "support": {"source": "https://github.com/PrestaShop/statssearch/tree/v2.0.2"}, "time": "2022-01-11T07:51:12+00:00"}, {"name": "prestashop/statsstock", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/PrestaShop/statsstock.git", "reference": "5e5961af2aae926cdf9a19f1c19be1c37397f736"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/statsstock/zipball/5e5961af2aae926cdf9a19f1c19be1c37397f736", "reference": "5e5961af2aae926cdf9a19f1c19be1c37397f736", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"prestashop/php-dev-tools": "^3.4"}, "type": "prestashop-module", "notification-url": "https://packagist.org/downloads/", "license": ["AFL-3.0"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}], "description": "PrestaShop module statsstock", "homepage": "https://github.com/PrestaShop/statsstock", "support": {"source": "https://github.com/PrestaShop/statsstock/tree/v2.0.1"}, "time": "2023-03-03T14:52:19+00:00"}, {"name": "prestashop/translationtools-bundle", "version": "v5.0.4", "source": {"type": "git", "url": "https://github.com/PrestaShop/TranslationToolsBundle.git", "reference": "4aaef89ee0b4913157a76559bed52f4f35b2fb70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PrestaShop/TranslationToolsBundle/zipball/4aaef89ee0b4913157a76559bed52f4f35b2fb70", "reference": "4aaef89ee0b4913157a76559bed52f4f35b2fb70", "shasum": ""}, "require": {"nikic/php-parser": "^4", "php": ">=7.2", "smarty/smarty": "^3.1 || ^4.0", "symfony/twig-bridge": "^3.4 || ^4.4 || ^5.0", "twig/twig": "^1.38 || ^2.7 || ^3.0"}, "require-dev": {"doctrine/common": "^2.10.0", "friendsofphp/php-cs-fixer": "^3.2", "phpunit/phpunit": "^7.5 || ^8.5", "symfony/framework-bundle": "^3.4 || ^4.4 || ^5.0", "symfony/symfony": "^4", "symfony/translation": "^3.4 || ^4.4 || ^5.0", "symfony/twig-bundle": "^4"}, "type": "bundle", "autoload": {"psr-4": {"PrestaShop\\TranslationToolsBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PrestaShop SA", "email": "<EMAIL>"}, {"name": "PrestaShop Community", "homepage": "https://contributors.prestashop.com/"}], "description": "Translation tools for PrestaShop", "keywords": ["parser", "prestashop", "translation"], "support": {"issues": "https://github.com/PrestaShop/TranslationToolsBundle/issues", "source": "https://github.com/PrestaShop/TranslationToolsBundle/tree/v5.0.4"}, "time": "2022-09-01T08:33:26+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.1"}, "time": "2021-03-05T17:36:06+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-factory", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "time": "2019-04-30T12:38:16+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/link", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/link.git", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link/zipball/eea8e8662d5cd3ae4517c9b864493f59fca95562", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "support": {"source": "https://github.com/php-fig/link/tree/master"}, "time": "2016-10-28T16:06:13+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.6.1", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "430d14c01836b77c28092883d195a43ce413ee32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/430d14c01836b77c28092883d195a43ce413ee32", "reference": "430d14c01836b77c28092883d195a43ce413ee32", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "php": ">=7.2.5", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0"}, "conflict": {"doctrine/doctrine-cache-bundle": "<1.3.1", "doctrine/persistence": "<1.3"}, "require-dev": {"doctrine/dbal": "^2.10|^3.0", "doctrine/doctrine-bundle": "^1.11|^2.0", "doctrine/orm": "^2.5", "nyholm/psr7": "^1.1", "symfony/browser-kit": "^4.4|^5.0", "symfony/doctrine-bridge": "^4.4|^5.0", "symfony/dom-crawler": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/monolog-bridge": "^4.0|^5.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^4.4.9|^5.0.9", "symfony/psr-http-message-bridge": "^1.1", "symfony/security-bundle": "^4.4|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.6.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": "src/"}, "exclude-from-classmap": ["/tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "support": {"issues": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/issues", "source": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/tree/v5.6.1"}, "abandoned": "Symfony", "time": "2020-08-25T19:10:18+00:00"}, {"name": "smarty/smarty", "version": "v4.3.4", "source": {"type": "git", "url": "https://github.com/smarty-php/smarty.git", "reference": "3931d8f54b8f7a4ffab538582d34d4397ba8daa5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smarty-php/smarty/zipball/3931d8f54b8f7a4ffab538582d34d4397ba8daa5", "reference": "3931d8f54b8f7a4ffab538582d34d4397ba8daa5", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^7.5", "smarty/smarty-lexer": "^3.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"classmap": ["libs/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://www.iwink.nl/"}], "description": "Smarty - the compiling PHP template engine", "homepage": "https://smarty-php.github.io/smarty/", "keywords": ["templating"], "support": {"forum": "https://github.com/smarty-php/smarty/discussions", "issues": "https://github.com/smarty-php/smarty/issues", "source": "https://github.com/smarty-php/smarty/tree/v4.3.4"}, "time": "2023-09-14T10:59:08+00:00"}, {"name": "soundasleep/html2text", "version": "0.5.0", "source": {"type": "git", "url": "https://github.com/soundasleep/html2text.git", "reference": "cdb89f6ffa2c4cc78f8ed9ea6ee0594a9133ccad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/soundasleep/html2text/zipball/cdb89f6ffa2c4cc78f8ed9ea6ee0594a9133ccad", "reference": "cdb89f6ffa2c4cc78f8ed9ea6ee0594a9133ccad", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": ">=4.0", "soundasleep/component-tests": "dev-master"}, "type": "library", "autoload": {"psr-4": {"Html2Text\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["EPL-1.0"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jevon.org", "role": "Developer"}], "description": "A PHP script to convert HTML into a plain text format", "homepage": "https://github.com/soundasleep/html2text", "keywords": ["email", "html", "php", "text"], "support": {"email": "<EMAIL>", "issues": "https://github.com/soundasleep/html2text/issues", "source": "https://github.com/soundasleep/html2text/tree/master"}, "time": "2017-04-19T22:01:50+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/contracts", "version": "v1.1.13", "source": {"type": "git", "url": "https://github.com/symfony/contracts.git", "reference": "9e27f5c175ecbd6fff554d839ff4a432da797168"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/contracts/zipball/9e27f5c175ecbd6fff554d839ff4a432da797168", "reference": "9e27f5c175ecbd6fff554d839ff4a432da797168", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/cache": "^1.0|^2.0|^3.0", "psr/container": "^1.0"}, "replace": {"symfony/cache-contracts": "self.version", "symfony/event-dispatcher-contracts": "self.version", "symfony/http-client-contracts": "self.version", "symfony/service-contracts": "self.version", "symfony/translation-contracts": "self.version"}, "require-dev": {"symfony/polyfill-intl-idn": "^1.10"}, "suggest": {"psr/event-dispatcher": "When using the EventDispatcher contracts", "symfony/cache-implementation": "", "symfony/event-dispatcher-implementation": "", "symfony/http-client-implementation": "", "symfony/service-implementation": "", "symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\": ""}, "exclude-from-classmap": ["**/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A set of abstractions extracted out of the Symfony components", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/contracts/tree/v1.1.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T13:16:42+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.8.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "a41bbcdc1105603b6d73a7d9a43a3788f8e0fb7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/a41bbcdc1105603b6d73a7d9a43a3788f8e0fb7d", "reference": "a41bbcdc1105603b6d73a7d9a43a3788f8e0fb7d", "shasum": ""}, "require": {"monolog/monolog": "^1.22 || ^2.0 || ^3.0", "php": ">=7.1.3", "symfony/config": "~4.4 || ^5.0 || ^6.0", "symfony/dependency-injection": "^4.4 || ^5.0 || ^6.0", "symfony/http-kernel": "~4.4 || ^5.0 || ^6.0", "symfony/monolog-bridge": "~4.4 || ^5.0 || ^6.0"}, "require-dev": {"symfony/console": "~4.4 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.2 || ^6.0", "symfony/yaml": "~4.4 || ^5.0 || ^6.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "https://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.8.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-10T14:24:36+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "5bbc823adecdae860bb64756d639ecfec17b050a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/5bbc823adecdae860bb64756d639ecfec17b050a", "reference": "5bbc823adecdae860bb64756d639ecfec17b050a", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "927013f3aac555983a5059aada98e1907d842695"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/927013f3aac555983a5059aada98e1907d842695", "reference": "927013f3aac555983a5059aada98e1907d842695", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "a3d9148e2c363588e05abbdd4ee4f971f0a5330c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/a3d9148e2c363588e05abbdd4ee4f971f0a5330c", "reference": "a3d9148e2c363588e05abbdd4ee4f971f0a5330c", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "639084e360537a19f9ee352433b84ce831f3d2da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/639084e360537a19f9ee352433b84ce831f3d2da", "reference": "639084e360537a19f9ee352433b84ce831f3d2da", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "19bd1e4fcd5b91116f14d8533c57831ed00571b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/19bd1e4fcd5b91116f14d8533c57831ed00571b6", "reference": "19bd1e4fcd5b91116f14d8533c57831ed00571b6", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "869329b1e9894268a8a61dabb69153029b7a8c97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/869329b1e9894268a8a61dabb69153029b7a8c97", "reference": "869329b1e9894268a8a61dabb69153029b7a8c97", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "9e8ecb5f92152187c4799efd3c96b78ccab18ff9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/9e8ecb5f92152187c4799efd3c96b78ccab18ff9", "reference": "9e8ecb5f92152187c4799efd3c96b78ccab18ff9", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936", "reference": "7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "707403074c8ea6e2edaf8794b0157a0bfa52157a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/707403074c8ea6e2edaf8794b0157a0bfa52157a", "reference": "707403074c8ea6e2edaf8794b0157a0bfa52157a", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "d444f85dddf65c7e57c58d8e5b3a4dbb593b1840"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/d444f85dddf65c7e57c58d8e5b3a4dbb593b1840", "reference": "d444f85dddf65c7e57c58d8e5b3a4dbb593b1840", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^4.4 || ^5.0 || ^6.0"}, "require-dev": {"nyholm/psr7": "^1.1", "psr/log": "^1.1 || ^2 || ^3", "symfony/browser-kit": "^4.4 || ^5.0 || ^6.0", "symfony/config": "^4.4 || ^5.0 || ^6.0", "symfony/event-dispatcher": "^4.4 || ^5.0 || ^6.0", "symfony/framework-bundle": "^4.4 || ^5.0 || ^6.0", "symfony/http-kernel": "^4.4 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.4@dev || ^6.0"}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-main": "2.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "http://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-09-05T10:34:54+00:00"}, {"name": "symfony/swiftmailer-bundle", "version": "v3.5.4", "source": {"type": "git", "url": "https://github.com/symfony/swiftmailer-bundle.git", "reference": "9daab339f226ac958192bf89836cb3378cc0e652"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/swiftmailer-bundle/zipball/9daab339f226ac958192bf89836cb3378cc0e652", "reference": "9daab339f226ac958192bf89836cb3378cc0e652", "shasum": ""}, "require": {"php": ">=7.1", "swiftmailer/swiftmailer": "^6.1.3", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0"}, "conflict": {"twig/twig": "<1.41|>=2.0,<2.10"}, "require-dev": {"symfony/console": "^4.4|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/phpunit-bridge": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SwiftmailerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony SwiftmailerBundle", "homepage": "http://symfony.com", "support": {"issues": "https://github.com/symfony/swiftmailer-bundle/issues", "source": "https://github.com/symfony/swiftmailer-bundle/tree/v3.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2022-02-06T08:03:40+00:00"}, {"name": "symfony/symfony", "version": "v4.4.51", "source": {"type": "git", "url": "https://github.com/symfony/symfony.git", "reference": "9926d13361941c4a8abef7e8d7cb8a1997409b9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/symfony/zipball/9926d13361941c4a8abef7e8d7cb8a1997409b9f", "reference": "9926d13361941c4a8abef7e8d7cb8a1997409b9f", "shasum": ""}, "require": {"doctrine/event-manager": "~1.0", "doctrine/persistence": "^1.3|^2|^3", "ext-xml": "*", "friendsofphp/proxy-manager-lts": "^1.0.2", "php": ">=7.1.3", "psr/cache": "^1.0|^2.0", "psr/container": "^1.0", "psr/link": "^1.0", "psr/log": "^1|^2", "symfony/contracts": "^1.1.8", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-icu": "~1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "twig/twig": "^1.43|^2.13|^3.0.4"}, "conflict": {"doctrine/dbal": "<2.7", "egulias/email-validator": "~3.0.0", "masterminds/html5": "<2.6", "monolog/monolog": ">=2", "ocramius/proxy-manager": "<2.1", "phpdocumentor/reflection-docblock": "<3.0|>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0|1.3.*", "phpunit/phpunit": "<5.4.3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/cache-implementation": "1.0|2.0", "psr/container-implementation": "1.0", "psr/event-dispatcher-implementation": "1.0", "psr/http-client-implementation": "1.0", "psr/link-implementation": "1.0", "psr/log-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0|2.0", "symfony/cache-implementation": "1.0|2.0", "symfony/event-dispatcher-implementation": "1.1", "symfony/http-client-implementation": "1.1|2.0", "symfony/service-implementation": "1.0|2.0", "symfony/translation-implementation": "1.0|2.0"}, "replace": {"symfony/amazon-mailer": "self.version", "symfony/asset": "self.version", "symfony/browser-kit": "self.version", "symfony/cache": "self.version", "symfony/config": "self.version", "symfony/console": "self.version", "symfony/css-selector": "self.version", "symfony/debug": "self.version", "symfony/debug-bundle": "self.version", "symfony/dependency-injection": "self.version", "symfony/doctrine-bridge": "self.version", "symfony/dom-crawler": "self.version", "symfony/dotenv": "self.version", "symfony/error-handler": "self.version", "symfony/event-dispatcher": "self.version", "symfony/expression-language": "self.version", "symfony/filesystem": "self.version", "symfony/finder": "self.version", "symfony/form": "self.version", "symfony/framework-bundle": "self.version", "symfony/google-mailer": "self.version", "symfony/http-client": "self.version", "symfony/http-foundation": "self.version", "symfony/http-kernel": "self.version", "symfony/inflector": "self.version", "symfony/intl": "self.version", "symfony/ldap": "self.version", "symfony/lock": "self.version", "symfony/mailchimp-mailer": "self.version", "symfony/mailer": "self.version", "symfony/mailgun-mailer": "self.version", "symfony/messenger": "self.version", "symfony/mime": "self.version", "symfony/monolog-bridge": "self.version", "symfony/options-resolver": "self.version", "symfony/postmark-mailer": "self.version", "symfony/process": "self.version", "symfony/property-access": "self.version", "symfony/property-info": "self.version", "symfony/proxy-manager-bridge": "self.version", "symfony/routing": "self.version", "symfony/security": "self.version", "symfony/security-bundle": "self.version", "symfony/security-core": "self.version", "symfony/security-csrf": "self.version", "symfony/security-guard": "self.version", "symfony/security-http": "self.version", "symfony/sendgrid-mailer": "self.version", "symfony/serializer": "self.version", "symfony/stopwatch": "self.version", "symfony/templating": "self.version", "symfony/translation": "self.version", "symfony/twig-bridge": "self.version", "symfony/twig-bundle": "self.version", "symfony/validator": "self.version", "symfony/var-dumper": "self.version", "symfony/var-exporter": "self.version", "symfony/web-link": "self.version", "symfony/web-profiler-bundle": "self.version", "symfony/web-server-bundle": "self.version", "symfony/workflow": "self.version", "symfony/yaml": "self.version"}, "require-dev": {"cache/integration-tests": "dev-master", "composer/package-versions-deprecated": "^1.8", "doctrine/annotations": "^1.10.4", "doctrine/cache": "^1.6|^2.0", "doctrine/collections": "~1.0", "doctrine/data-fixtures": "^1.1", "doctrine/dbal": "^2.7|^3.0", "doctrine/orm": "^2.6.3", "egulias/email-validator": "^2.1.10|^3.1", "guzzlehttp/promises": "^1.4", "masterminds/html5": "^2.6", "monolog/monolog": "^1.25.1", "nyholm/psr7": "^1.0", "paragonie/sodium_compat": "^1.8", "php-http/httplug": "^1.0|^2.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "predis/predis": "~1.1", "psr/http-client": "^1.0", "psr/simple-cache": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.2", "symfony/security-acl": "~2.8|~3.0", "twig/cssinliner-extra": "^2.12|^3", "twig/inky-extra": "^2.12|^3", "twig/markdown-extra": "^2.12|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Bundle\\": "src/Symfony/Bundle/", "Symfony\\Component\\": "src/Symfony/Component/", "Symfony\\Bridge\\Twig\\": "src/Symfony/Bridge/Twig/", "Symfony\\Bridge\\Monolog\\": "src/Symfony/Bridge/Monolog/", "Symfony\\Bridge\\Doctrine\\": "src/Symfony/Bridge/Doctrine/", "Symfony\\Bridge\\ProxyManager\\": "src/Symfony/Bridge/ProxyManager/"}, "classmap": ["src/Symfony/Component/Intl/Resources/stubs"], "exclude-from-classmap": ["**/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "The Symfony PHP framework", "homepage": "https://symfony.com", "keywords": ["framework"], "support": {"issues": "https://github.com/symfony/symfony/issues", "source": "https://github.com/symfony/symfony/tree/v4.4.51"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-10T13:31:44+00:00"}, {"name": "tecnickcom/tcpdf", "version": "6.4.4", "source": {"type": "git", "url": "https://github.com/tecnickcom/TCPDF.git", "reference": "42cd0f9786af7e5db4fcedaa66f717b0d0032320"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/TCPDF/zipball/42cd0f9786af7e5db4fcedaa66f717b0d0032320", "reference": "42cd0f9786af7e5db4fcedaa66f717b0d0032320", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"classmap": ["config", "include", "tcpdf.php", "tcpdf_parser.php", "tcpdf_import.php", "tcpdf_barcodes_1d.php", "tcpdf_barcodes_2d.php", "include/tcpdf_colors.php", "include/tcpdf_filters.php", "include/tcpdf_font_data.php", "include/tcpdf_fonts.php", "include/tcpdf_images.php", "include/tcpdf_static.php", "include/barcodes/datamatrix.php", "include/barcodes/pdf417.php", "include/barcodes/qrcode.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "TCPDF is a PHP class for generating PDF documents and barcodes.", "homepage": "http://www.tcpdf.org/", "keywords": ["PDFD32000-2008", "TCPDF", "barcodes", "datamatrix", "pdf", "pdf417", "qrcode"], "support": {"issues": "https://github.com/tecnickcom/TCPDF/issues", "source": "https://github.com/tecnickcom/TCPDF/tree/6.4.4"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_donations&currency_code=GBP&business=<EMAIL>&item_name=donation%20for%20tcpdf%20project", "type": "custom"}], "time": "2021-12-31T08:39:24+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "2.2.5", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "da444caae6aca7a19c0c140f68c6182e337d5b1c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/da444caae6aca7a19c0c140f68c6182e337d5b1c", "reference": "da444caae6aca7a19c0c140f68c6182e337d5b1c", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.4"}, "time": "2021-12-08T09:12:39+00:00"}, {"name": "tubalmartin/cssmin", "version": "v4.1.1", "source": {"type": "git", "url": "https://github.com/tubalmartin/YUI-CSS-compressor-PHP-port.git", "reference": "3cbf557f4079d83a06f9c3ff9b957c022d7805cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tubalmartin/YUI-CSS-compressor-PHP-port/zipball/3cbf557f4079d83a06f9c3ff9b957c022d7805cf", "reference": "3cbf557f4079d83a06f9c3ff9b957c022d7805cf", "shasum": ""}, "require": {"ext-pcre": "*", "php": ">=5.3.2"}, "require-dev": {"cogpowered/finediff": "0.3.*", "phpunit/phpunit": "4.8.*"}, "bin": ["cssmin"], "type": "library", "autoload": {"psr-4": {"tubalmartin\\CssMin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://tubalmartin.me/"}], "description": "A PHP port of the YUI CSS compressor", "homepage": "https://github.com/tubalmartin/YUI-CSS-compressor-PHP-port", "keywords": ["compress", "compressor", "css", "cssmin", "minify", "yui"], "support": {"issues": "https://github.com/tubalmartin/YUI-CSS-compressor-PHP-port/issues", "source": "https://github.com/tubalmartin/YUI-CSS-compressor-PHP-port"}, "time": "2018-01-15T15:26:51+00:00"}, {"name": "twig/twig", "version": "v3.4.3", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "c38fd6b0b7f370c198db91ffd02e23b517426b58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/c38fd6b0b7f370c198db91ffd02e23b517426b58", "reference": "c38fd6b0b7f370c198db91ffd02e23b517426b58", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.4.3"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2022-09-28T08:42:51+00:00"}, {"name": "<PERSON><PERSON><PERSON>/jsonp-callback-validator", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/willdurand/JsonpCallbackValidator.git", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/JsonpCallbackValidator/zipball/1a7d388bb521959e612ef50c5c7b1691b097e909", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~3.7"}, "type": "library", "autoload": {"psr-0": {"JsonpCallbackValidator": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.willdurand.fr"}], "description": "JSONP callback validator.", "support": {"issues": "https://github.com/willdurand/JsonpCallbackValidator/issues", "source": "https://github.com/willdurand/JsonpCallbackValidator/tree/master"}, "time": "2014-01-20T22:35:06+00:00"}, {"name": "will<PERSON><PERSON>/negotiation", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/willdurand/Negotiation.git", "reference": "68e9ea0553ef6e2ee8db5c1d98829f111e623ec2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/Negotiation/zipball/68e9ea0553ef6e2ee8db5c1d98829f111e623ec2", "reference": "68e9ea0553ef6e2ee8db5c1d98829f111e623ec2", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"symfony/phpunit-bridge": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Negotiation\\": "src/Negotiation"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Content Negotiation tools for PHP provided as a standalone library.", "homepage": "http://williamdurand.fr/Negotiation/", "keywords": ["accept", "content", "format", "header", "negotiation"], "support": {"issues": "https://github.com/willdurand/Negotiation/issues", "source": "https://github.com/willdurand/Negotiation/tree/3.1.0"}, "time": "2022-01-30T20:08:53+00:00"}], "packages-dev": [{"name": "behat/behat", "version": "v3.10.0", "source": {"type": "git", "url": "https://github.com/Behat/Behat.git", "reference": "a55661154079cf881ef643b303bfaf67bae3a09f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Behat/zipball/a55661154079cf881ef643b303bfaf67bae3a09f", "reference": "a55661154079cf881ef643b303bfaf67bae3a09f", "shasum": ""}, "require": {"behat/gherkin": "^4.9.0", "behat/transliterator": "^1.2", "ext-mbstring": "*", "php": "^7.2 || ^8.0", "psr/container": "^1.0", "symfony/config": "^4.4 || ^5.0 || ^6.0", "symfony/console": "^4.4 || ^5.0 || ^6.0", "symfony/dependency-injection": "^4.4 || ^5.0 || ^6.0", "symfony/event-dispatcher": "^4.4 || ^5.0 || ^6.0", "symfony/translation": "^4.4 || ^5.0 || ^6.0", "symfony/yaml": "^4.4 || ^5.0 || ^6.0"}, "require-dev": {"container-interop/container-interop": "^1.2", "herrera-io/box": "~1.6.1", "phpunit/phpunit": "^8.5 || ^9.0", "symfony/process": "^4.4 || ^5.0 || ^6.0", "vimeo/psalm": "^4.8"}, "suggest": {"ext-dom": "Needed to output test results in JUnit format."}, "bin": ["bin/behat"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Behat\\Hook\\": "src/Behat/Hook/", "Behat\\Step\\": "src/Behat/Step/", "Behat\\Behat\\": "src/Behat/Behat/", "Behat\\Testwork\\": "src/Behat/Testwork/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Scenario-oriented BDD framework for PHP", "homepage": "http://behat.org/", "keywords": ["Agile", "BDD", "ScenarioBDD", "Scrum", "StoryBDD", "User story", "business", "development", "documentation", "examples", "symfony", "testing"], "support": {"issues": "https://github.com/Behat/Behat/issues", "source": "https://github.com/Behat/Behat/tree/v3.10.0"}, "time": "2021-11-02T20:09:40+00:00"}, {"name": "behat/gherkin", "version": "v4.9.0", "source": {"type": "git", "url": "https://github.com/Behat/Gherkin.git", "reference": "0bc8d1e30e96183e4f36db9dc79caead300beff4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Gherkin/zipball/0bc8d1e30e96183e4f36db9dc79caead300beff4", "reference": "0bc8d1e30e96183e4f36db9dc79caead300beff4", "shasum": ""}, "require": {"php": "~7.2|~8.0"}, "require-dev": {"cucumber/cucumber": "dev-gherkin-22.0.0", "phpunit/phpunit": "~8|~9", "symfony/yaml": "~3|~4|~5"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-0": {"Behat\\Gherkin": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Gherkin DSL parser for PHP", "homepage": "http://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "support": {"issues": "https://github.com/Behat/Gher<PERSON>/issues", "source": "https://github.com/Behat/Gherkin/tree/v4.9.0"}, "time": "2021-10-12T13:05:09+00:00"}, {"name": "behat/transliterator", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/Behat/Transliterator.git", "reference": "baac5873bac3749887d28ab68e2f74db3a4408af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Transliterator/zipball/baac5873bac3749887d28ab68e2f74db3a4408af", "reference": "baac5873bac3749887d28ab68e2f74db3a4408af", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"chuyskywalker/rolling-curl": "^3.1", "php-yaoi/php-yaoi": "^1.0", "phpunit/phpunit": "^8.5.25 || ^9.5.19"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Behat\\Transliterator\\": "src/Behat/Transliterator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Artistic-1.0"], "description": "String transliterator", "keywords": ["i18n", "slug", "transliterator"], "support": {"issues": "https://github.com/Behat/Transliterator/issues", "source": "https://github.com/Behat/Transliterator/tree/v1.5.0"}, "time": "2022-03-30T09:27:43+00:00"}, {"name": "composer/pcre", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "67a32d7d6f9f560b726ab25a061b38ff3a80c560"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/67a32d7d6f9f560b726ab25a061b38ff3a80c560", "reference": "67a32d7d6f9f560b726ab25a061b38ff3a80c560", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/1.0.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-21T20:24:37+00:00"}, {"name": "composer/semver", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/3953f23262f2bff1919fc82183ad9acb13ff62c9", "reference": "3953f23262f2bff1919fc82183ad9acb13ff62c9", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-04-01T19:23:25+00:00"}, {"name": "composer/xdebug-handler", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "9e36aeed4616366d2b690bdce11f71e9178c579a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/9e36aeed4616366d2b690bdce11f71e9178c579a", "reference": "9e36aeed4616366d2b690bdce11f71e9178c579a", "shasum": ""}, "require": {"composer/pcre": "^1", "php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^4.2 || ^5.0 || ^6.0"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/2.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-24T20:20:32+00:00"}, {"name": "ergebnis/composer-normalize", "version": "2.13.3", "source": {"type": "git", "url": "https://github.com/ergebnis/composer-normalize.git", "reference": "eff003890c655ee0e4b6ac5d4c5b40ce61247f7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ergebnis/composer-normalize/zipball/eff003890c655ee0e4b6ac5d4c5b40ce61247f7c", "reference": "eff003890c655ee0e4b6ac5d4c5b40ce61247f7c", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0.0", "ergebnis/json-normalizer": "^1.0.3", "ergebnis/json-printer": "^3.1.1", "justinrainbow/json-schema": "^5.2.10", "localheinz/diff": "^1.1.1", "php": "^7.2 || ^8.0"}, "require-dev": {"composer/composer": "^1.10.19 || ^2.0.8", "ergebnis/license": "^1.1.0", "ergebnis/php-cs-fixer-config": "^2.13.0", "ergebnis/phpstan-rules": "~0.15.3", "ergebnis/test-util": "^1.4.0", "phpstan/extension-installer": "^1.1.0", "phpstan/phpstan": "~0.12.80", "phpstan/phpstan-deprecation-rules": "~0.12.6", "phpstan/phpstan-phpunit": "~0.12.18", "phpstan/phpstan-strict-rules": "~0.12.9", "phpunit/phpunit": "^8.5.14", "psalm/plugin-phpunit": "~0.15.0", "symfony/filesystem": "^5.2.4", "vimeo/psalm": "^4.6.2"}, "type": "composer-plugin", "extra": {"class": "Ergebnis\\Composer\\Normalize\\NormalizePlugin", "composer-normalize": {"indent-size": 2, "indent-style": "space"}}, "autoload": {"psr-4": {"Ergebnis\\Composer\\Normalize\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a composer plugin for normalizing composer.json.", "homepage": "https://github.com/ergebnis/composer-normalize", "keywords": ["composer", "normalize", "normalizer", "plugin"], "support": {"issues": "https://github.com/ergebnis/composer-normalize/issues", "source": "https://github.com/ergebnis/composer-normalize"}, "funding": [{"url": "https://github.com/localheinz", "type": "github"}], "time": "2021-03-06T14:00:23+00:00"}, {"name": "ergebnis/json-normalizer", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/ergebnis/json-normalizer.git", "reference": "4a7f064ce34d5a2e382564565cdd433dbc5b9494"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ergebnis/json-normalizer/zipball/4a7f064ce34d5a2e382564565cdd433dbc5b9494", "reference": "4a7f064ce34d5a2e382564565cdd433dbc5b9494", "shasum": ""}, "require": {"ergebnis/json-printer": "^3.1.1", "ext-json": "*", "justinrainbow/json-schema": "^5.2.10", "php": "^7.2 || ^8.0"}, "require-dev": {"ergebnis/license": "^1.1.0", "ergebnis/php-cs-fixer-config": "^2.10.0", "ergebnis/phpstan-rules": "~0.15.3", "ergebnis/test-util": "^1.4.0", "infection/infection": "~0.15.3", "jangregor/phpstan-prophecy": "~0.8.1", "phpstan/extension-installer": "^1.1.0", "phpstan/phpstan": "~0.12.80", "phpstan/phpstan-deprecation-rules": "~0.12.6", "phpstan/phpstan-phpunit": "~0.12.17", "phpstan/phpstan-strict-rules": "~0.12.9", "phpunit/phpunit": "^8.5.14", "psalm/plugin-phpunit": "~0.12.2", "vimeo/psalm": "^3.18"}, "type": "library", "autoload": {"psr-4": {"Ergebnis\\Json\\Normalizer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides generic and vendor-specific normalizers for normalizing JSON documents.", "homepage": "https://github.com/ergebnis/json-normalizer", "keywords": ["json", "normalizer"], "support": {"issues": "https://github.com/ergebnis/json-normalizer/issues", "source": "https://github.com/ergebnis/json-normalizer"}, "funding": [{"url": "https://github.com/localheinz", "type": "github"}], "time": "2021-03-06T13:33:57+00:00"}, {"name": "ergebnis/json-printer", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/ergebnis/json-printer.git", "reference": "e4190dadd9937a77d8afcaf2b6c42a528ab367d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ergebnis/json-printer/zipball/e4190dadd9937a77d8afcaf2b6c42a528ab367d6", "reference": "e4190dadd9937a77d8afcaf2b6c42a528ab367d6", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"ergebnis/license": "^1.0.0", "ergebnis/php-cs-fixer-config": "^2.2.1", "ergebnis/phpstan-rules": "~0.15.2", "ergebnis/test-util": "^1.1.0", "infection/infection": "~0.15.3", "phpstan/extension-installer": "^1.0.4", "phpstan/phpstan": "~0.12.40", "phpstan/phpstan-deprecation-rules": "~0.12.5", "phpstan/phpstan-phpunit": "~0.12.16", "phpstan/phpstan-strict-rules": "~0.12.4", "phpunit/phpunit": "^8.5.8", "psalm/plugin-phpunit": "~0.11.0", "vimeo/psalm": "^3.14.2"}, "type": "library", "autoload": {"psr-4": {"Ergebnis\\Json\\Printer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a JSON printer, allowing for flexible indentation.", "homepage": "https://github.com/ergebnis/json-printer", "keywords": ["formatter", "json", "printer"], "support": {"issues": "https://github.com/ergebnis/json-printer/issues", "source": "https://github.com/ergebnis/json-printer"}, "funding": [{"url": "https://github.com/localheinz", "type": "github"}], "time": "2020-08-30T12:17:03+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/PHP-CS-Fixer.git", "reference": "47177af1cfb9dab5d1cc4daf91b7179c2efe7fad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/PHP-CS-Fixer/zipball/47177af1cfb9dab5d1cc4daf91b7179c2efe7fad", "reference": "47177af1cfb9dab5d1cc4daf91b7179c2efe7fad", "shasum": ""}, "require": {"composer/semver": "^3.2", "composer/xdebug-handler": "^2.0", "doctrine/annotations": "^1.12", "ext-json": "*", "ext-tokenizer": "*", "php": "^7.2.5 || ^8.0", "php-cs-fixer/diff": "^2.0", "symfony/console": "^4.4.20 || ^5.1.3 || ^6.0", "symfony/event-dispatcher": "^4.4.20 || ^5.0 || ^6.0", "symfony/filesystem": "^4.4.20 || ^5.0 || ^6.0", "symfony/finder": "^4.4.20 || ^5.0 || ^6.0", "symfony/options-resolver": "^4.4.20 || ^5.0 || ^6.0", "symfony/polyfill-mbstring": "^1.23", "symfony/polyfill-php80": "^1.23", "symfony/polyfill-php81": "^1.23", "symfony/process": "^4.4.20 || ^5.0 || ^6.0", "symfony/stopwatch": "^4.4.20 || ^5.0 || ^6.0"}, "require-dev": {"justinrainbow/json-schema": "^5.2", "keradus/cli-executor": "^1.5", "mikey179/vfsstream": "^1.6.8", "php-coveralls/php-coveralls": "^2.5.2", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.2", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.2.1", "phpspec/prophecy": "^1.15", "phpspec/prophecy-phpunit": "^1.1 || ^2.0", "phpunit/phpunit": "^8.5.21 || ^9.5", "phpunitgoodpractices/polyfill": "^1.5", "phpunitgoodpractices/traits": "^1.9.1", "symfony/phpunit-bridge": "^5.2.4 || ^6.0", "symfony/yaml": "^4.4.20 || ^5.0 || ^6.0"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "bin": ["php-cs-fixer"], "type": "application", "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "support": {"issues": "https://github.com/FriendsOfPHP/PHP-CS-Fixer/issues", "source": "https://github.com/FriendsOfPHP/PHP-CS-Fixer/tree/v3.4.0"}, "funding": [{"url": "https://github.com/keradus", "type": "github"}], "time": "2021-12-11T16:25:08+00:00"}, {"name": "johnkary/phpunit-speedtrap", "version": "v3.3.0", "source": {"type": "git", "url": "https://github.com/johnkary/phpunit-speedtrap.git", "reference": "9ba81d42676da31366c85d3ff8c10a8352d02030"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/johnkary/phpunit-speedtrap/zipball/9ba81d42676da31366c85d3ff8c10a8352d02030", "reference": "9ba81d42676da31366c85d3ff8c10a8352d02030", "shasum": ""}, "require": {"php": ">=7.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"psr-4": {"JohnKary\\PHPUnit\\Listener\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Find and report on slow tests in your PHPUnit test suite", "homepage": "https://github.com/johnkary/phpunit-speedtrap", "keywords": ["phpunit", "profile", "slow"], "support": {"issues": "https://github.com/johnkary/phpunit-speedtrap/issues", "source": "https://github.com/johnkary/phpunit-speedtrap/tree/v3.3.0"}, "time": "2020-12-18T16:20:16+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.12", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "ad87d5a5ca981228e0e205c2bc7dfb8e24559b60"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/ad87d5a5ca981228e0e205c2bc7dfb8e24559b60", "reference": "ad87d5a5ca981228e0e205c2bc7dfb8e24559b60", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/justinrainbow/json-schema/issues", "source": "https://github.com/justinrainbow/json-schema/tree/5.2.12"}, "time": "2022-04-13T08:02:27+00:00"}, {"name": "localheinz/diff", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/localheinz/diff.git", "reference": "851bb20ea8358c86f677f5f111c4ab031b1c764c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/localheinz/diff/zipball/851bb20ea8358c86f677f5f111c4ab031b1c764c", "reference": "851bb20ea8358c86f677f5f111c4ab031b1c764c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^4.2 || ^5"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fork of sebastian/diff for use with ergebnis/composer-normalize", "homepage": "https://github.com/localheinz/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"source": "https://github.com/localheinz/diff/tree/main"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-07-06T04:49:32+00:00"}, {"name": "mikey179/vfsstream", "version": "v1.6.11", "source": {"type": "git", "url": "https://github.com/bovigo/vfsStream.git", "reference": "250c0825537d501e327df879fb3d4cd751933b85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bovigo/vfsStream/zipball/250c0825537d501e327df879fb3d4cd751933b85", "reference": "250c0825537d501e327df879fb3d4cd751933b85", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.5|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-0": {"org\\bovigo\\vfs\\": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://frankkleine.de/", "role": "Developer"}], "description": "Virtual file system to mock the real file system in unit tests.", "homepage": "http://vfs.bovigo.org/", "support": {"issues": "https://github.com/bovigo/vfsStream/issues", "source": "https://github.com/bovigo/vfsStream/tree/master", "wiki": "https://github.com/bovigo/vfsStream/wiki"}, "time": "2021-09-25T08:05:01+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/14daed4296fae74d9e3201d2c4925d1acb7aa614", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2022-03-03T13:19:32+00:00"}, {"name": "phar-io/manifest", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "97803eca37d319dfa7826cc2437fc020857acb53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/97803eca37d319dfa7826cc2437fc020857acb53", "reference": "97803eca37d319dfa7826cc2437fc020857acb53", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.3"}, "time": "2021-07-20T11:28:43+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "php-cs-fixer/diff", "version": "v2.0.2", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/diff.git", "reference": "29dc0d507e838c4580d018bd8b5cb412474f7ec3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/diff/zipball/29dc0d507e838c4580d018bd8b5cb412474f7ec3", "reference": "29dc0d507e838c4580d018bd8b5cb412474f7ec3", "shasum": ""}, "require": {"php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.7.23 || ^6.4.3 || ^7.0", "symfony/process": "^3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "sebastian/diff v3 backport support for PHP 5.6+", "homepage": "https://github.com/PHP-CS-Fixer", "keywords": ["diff"], "support": {"issues": "https://github.com/PHP-CS-Fixer/diff/issues", "source": "https://github.com/PHP-CS-Fixer/diff/tree/v2.0.2"}, "abandoned": true, "time": "2020-10-14T08:32:19+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "77a32518733312af16a44300404e945338981de3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/77a32518733312af16a44300404e945338981de3", "reference": "77a32518733312af16a44300404e945338981de3", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.6.1"}, "time": "2022-03-15T21:29:03+00:00"}, {"name": "phpspec/prophecy", "version": "v1.15.0", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "bbcd7380b0ebf3961ee21409db7b38bc31d69a13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/bbcd7380b0ebf3961ee21409db7b38bc31d69a13", "reference": "bbcd7380b0ebf3961ee21409db7b38bc31d69a13", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2", "php": "^7.2 || ~8.0, <8.2", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0 || ^7.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.15.0"}, "time": "2021-12-08T12:19:24+00:00"}, {"name": "phpstan/extension-installer", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/phpstan/extension-installer.git", "reference": "f06dbb052ddc394e7896fcd1cfcd533f9f6ace40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/extension-installer/zipball/f06dbb052ddc394e7896fcd1cfcd533f9f6ace40", "reference": "f06dbb052ddc394e7896fcd1cfcd533f9f6ace40", "shasum": ""}, "require": {"composer-plugin-api": "^2.0", "php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.8.0"}, "require-dev": {"composer/composer": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.2.0", "phpstan/phpstan-strict-rules": "^0.11 || ^0.12 || ^1.0"}, "type": "composer-plugin", "extra": {"class": "PHPStan\\ExtensionInstaller\\Plugin"}, "autoload": {"psr-4": {"PHPStan\\ExtensionInstaller\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Composer plugin for automatic installation of PHPStan extensions", "support": {"issues": "https://github.com/phpstan/extension-installer/issues", "source": "https://github.com/phpstan/extension-installer/tree/1.2.0"}, "time": "2022-10-17T12:59:16+00:00"}, {"name": "phpstan/phpstan", "version": "1.9.14", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "e5fcc96289cf737304286a9b505fbed091f02e58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/e5fcc96289cf737304286a9b505fbed091f02e58", "reference": "e5fcc96289cf737304286a9b505fbed091f02e58", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"issues": "https://github.com/phpstan/phpstan/issues", "source": "https://github.com/phpstan/phpstan/tree/1.9.14"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2023-01-19T10:47:09+00:00"}, {"name": "phpunit/php-code-coverage", "version": "7.0.15", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "819f92bba8b001d4363065928088de22f25a3a48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/819f92bba8b001d4363065928088de22f25a3a48", "reference": "819f92bba8b001d4363065928088de22f25a3a48", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": ">=7.2", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.1.3 || ^4.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}, "require-dev": {"phpunit/phpunit": "^8.2.2"}, "suggest": {"ext-xdebug": "^2.7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.15"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-07-26T12:20:09+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5", "reference": "42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:42:26+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.1"}, "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/2454ae1765516d20c4ffe103d85a58a9a3bd5662", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/2.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:20:02+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "9c1da83261628cb24b6a6df371b6e312b3954768"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/9c1da83261628cb24b6a6df371b6e312b3954768", "reference": "9c1da83261628cb24b6a6df371b6e312b3954768", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-token-stream/tree/3.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "abandoned": true, "time": "2021-07-26T12:15:06+00:00"}, {"name": "phpunit/phpunit", "version": "8.5.26", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "ef117c59fc4c54a979021b26d08a3373e386606d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/ef117c59fc4c54a979021b26d08a3373e386606d", "reference": "ef117c59fc4c54a979021b26d08a3373e386606d", "shasum": ""}, "require": {"doctrine/instantiator": "^1.3.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.0", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=7.2", "phpspec/prophecy": "^1.10.3", "phpunit/php-code-coverage": "^7.0.12", "phpunit/php-file-iterator": "^2.0.4", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1.2", "sebastian/comparator": "^3.0.2", "sebastian/diff": "^3.0.2", "sebastian/environment": "^4.2.3", "sebastian/exporter": "^3.1.2", "sebastian/global-state": "^3.0.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0.1", "sebastian/type": "^1.1.3", "sebastian/version": "^2.0.1"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "8.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/8.5.26"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-04-01T12:34:39+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/1de8cd5c010cb153fcd68b8d0f64606f523f7619", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:15:22+00:00"}, {"name": "sebastian/comparator", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1071dfcef776a57013124ff35e1fc41ccd294758", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758", "shasum": ""}, "require": {"php": ">=7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:04:30+00:00"}, {"name": "sebastian/diff", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:59:04+00:00"}, {"name": "sebastian/environment", "version": "4.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/4.2.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:53:42+00:00"}, {"name": "sebastian/exporter", "version": "3.1.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "0c32ea2e40dbf59de29f3b49bf375176ce7dd8db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/0c32ea2e40dbf59de29f3b49bf375176ce7dd8db", "reference": "0c32ea2e40dbf59de29f3b49bf375176ce7dd8db", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/3.1.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-11-11T13:51:24+00:00"}, {"name": "sebastian/global-state", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "de036ec91d55d2a9e0db2ba975b512cdb1c23921"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/de036ec91d55d2a9e0db2ba975b512cdb1c23921", "reference": "de036ec91d55d2a9e0db2ba975b512cdb1c23921", "shasum": ""}, "require": {"php": ">=7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-02-10T06:55:38+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/object-enumerator/zipball/e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:40:27+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:37:18+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/367dcba38d6e1977be014dc4b22f47a484dac7fb", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:34:24+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/31d35ca87926450c44eae7e2611d45a7a65ea8b3", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:30:19+00:00"}, {"name": "sebastian/type", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "0150cfbc4495ed2df3872fb31b26781e4e077eb4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/0150cfbc4495ed2df3872fb31b26781e4e077eb4", "reference": "0150cfbc4495ed2df3872fb31b26781e4e077eb4", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/1.1.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:25:11+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2016-10-03T07:35:21+00:00"}, {"name": "spaze/phpstan-disallowed-calls", "version": "v2.10.0", "source": {"type": "git", "url": "https://github.com/spaze/phpstan-disallowed-calls.git", "reference": "332969d0aa67ddb8597c2f1248268d69ebf5f9c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spaze/phpstan-disallowed-calls/zipball/332969d0aa67ddb8597c2f1248268d69ebf5f9c7", "reference": "332969d0aa67ddb8597c2f1248268d69ebf5f9c7", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.0"}, "require-dev": {"nette/neon": "^3.2", "nikic/php-parser": "^4.13", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpunit/phpunit": "^7.0 || ^9.4.2", "spaze/coding-standard": "^1.3"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Spaze\\PHPStan\\Rules\\Disallowed\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.michalspacek.cz"}], "description": "PHPStan rules to detect disallowed method & function calls, constant, namespace & superglobal usages", "keywords": ["static analysis"], "support": {"issues": "https://github.com/spaze/phpstan-disallowed-calls/issues", "source": "https://github.com/spaze/phpstan-disallowed-calls/tree/v2.10.0"}, "time": "2022-11-23T04:26:38+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v3.4.47", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "120273ad5d03a8deee08ca9260e2598f288f2bac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/120273ad5d03a8deee08ca9260e2598f288f2bac", "reference": "120273ad5d03a8deee08ca9260e2598f288f2bac", "shasum": ""}, "require": {"php": ">=5.3.3"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0|<6.4,>=6.0|9.1.2"}, "suggest": {"symfony/debug": "For tracking deprecated interfaces usages at runtime with DebugClassLoader"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"thanks": {"name": "phpunit/phpunit", "url": "https://github.com/sebastian<PERSON>mann/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PHPUnit Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/phpunit-bridge/tree/v3.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-11-13T16:28:59+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/34a41e998c2183e22995f158c581e7b5e755ab9e", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.1"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2021-07-28T10:34:58+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "aliases": [{"package": "ezyang/htmlpurifier", "version": "9999999-dev", "alias": "v4.14.0", "alias_normalized": "********"}, {"package": "lcobucci/jwt", "version": "dev-3.4.6-patch", "alias": "3.4.6", "alias_normalized": "*******"}], "minimum-stability": "dev", "stability-flags": {"ezyang/htmlpurifier": 20, "lcobucci/jwt": 20, "prestashop/laminas-code-lts": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=7.2.5", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-zip": "*"}, "platform-dev": [], "platform-overrides": {"php": "7.2.5"}, "plugin-api-version": "2.6.0"}